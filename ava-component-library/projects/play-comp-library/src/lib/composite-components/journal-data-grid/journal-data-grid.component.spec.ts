import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { JournalDataGridComponent, JournalEntry } from './journal-data-grid.component';

describe('JournalDataGridComponent', () => {
  let component: JournalDataGridComponent;
  let fixture: ComponentFixture<JournalDataGridComponent>;

  const mockJournalData: JournalEntry[] = [
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388237',
      date: 'mm/dd/yyyy',
      journalDescription: 'Posted journal entry',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: false
    },
    {
      journalId: '388238',
      date: 'mm/dd/yyyy',
      journalDescription: 'Rejected entry',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [JournalDataGridComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(JournalDataGridComponent);
    component = fixture.componentInstance;
    component.data = mockJournalData;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display journal entries', () => {
    expect(component.filteredData.length).toBe(3);
    expect(component.filteredData[0].journalId).toBe('388236');
  });

  it('should show search input when config.showSearch is true', () => {
    component.config.showSearch = true;
    fixture.detectChanges();
    
    const searchInput = fixture.debugElement.query(By.css('ava-textbox'));
    expect(searchInput).toBeTruthy();
  });

  it('should hide search input when config.showSearch is false', () => {
    component.config.showSearch = false;
    fixture.detectChanges();
    
    const searchInput = fixture.debugElement.query(By.css('ava-textbox'));
    expect(searchInput).toBeFalsy();
  });

  it('should show create button when config.showCreateButton is true', () => {
    component.config.showCreateButton = true;
    fixture.detectChanges();
    
    const createButton = fixture.debugElement.query(By.css('.create-button'));
    expect(createButton).toBeTruthy();
  });

  it('should emit createClick event when create button is clicked', () => {
    spyOn(component.createClick, 'emit');
    
    component.onCreateJournalEntry();
    
    expect(component.createClick.emit).toHaveBeenCalled();
  });

  it('should emit rowClick event when journal ID is clicked', () => {
    spyOn(component.rowClick, 'emit');
    
    const testRow = mockJournalData[0];
    component.onRowClick(testRow);
    
    expect(component.rowClick.emit).toHaveBeenCalledWith(testRow);
  });

  it('should emit documentClick event when document icon is clicked', () => {
    spyOn(component.documentClick, 'emit');
    
    const testRow = mockJournalData[0];
    const mockEvent = new Event('click');
    component.onDocumentClick(testRow, mockEvent);
    
    expect(component.documentClick.emit).toHaveBeenCalledWith(testRow);
  });

  it('should filter data based on search term', () => {
    component.searchTerm = 'template';
    
    const filtered = component.filteredData;
    
    expect(filtered.length).toBe(1);
    expect(filtered[0].journalDescription).toContain('template');
  });

  it('should return correct status tag color', () => {
    expect(component.getStatusTagColor('Posted')).toBe('success');
    expect(component.getStatusTagColor('Template')).toBe('info');
    expect(component.getStatusTagColor('Rejected')).toBe('error');
    expect(component.getStatusTagColor('Ready to Approve')).toBe('warning');
    expect(component.getStatusTagColor('Draft')).toBe('default');
  });

  it('should return outlined variant for all status tags', () => {
    expect(component.getStatusTagVariant('Posted')).toBe('outlined');
    expect(component.getStatusTagVariant('Template')).toBe('outlined');
    expect(component.getStatusTagVariant('Rejected')).toBe('outlined');
  });

  it('should track journal entries by journalId', () => {
    const testEntry = mockJournalData[0];
    const result = component.trackByJournalId(0, testEntry);
    
    expect(result).toBe('388236');
  });

  it('should emit search event when search term changes', () => {
    spyOn(component.searchChange, 'emit');
    spyOn(component.journalEvent, 'emit');
    
    const mockEvent = {
      target: { value: 'test search' }
    } as any;
    
    component.onSearchChange(mockEvent);
    
    expect(component.searchChange.emit).toHaveBeenCalledWith('test search');
    expect(component.journalEvent.emit).toHaveBeenCalledWith({
      type: 'search',
      row: undefined,
      searchTerm: 'test search'
    });
  });

  it('should use custom columns when provided', () => {
    const customColumns = ['journalId', 'date', 'journalStatus'];
    component.config.customColumns = customColumns;
    
    expect(component.displayedColumns).toEqual(customColumns);
  });

  it('should use default columns when custom columns not provided', () => {
    component.config.customColumns = undefined;
    
    const expectedColumns = [
      'journalId',
      'date', 
      'journalDescription',
      'journalStatus',
      'sourceTransaction',
      'drCrTotals',
      'documents'
    ];
    
    expect(component.displayedColumns).toEqual(expectedColumns);
  });
});
