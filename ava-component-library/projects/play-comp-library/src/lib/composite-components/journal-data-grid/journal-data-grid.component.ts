import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataGridComponent } from '../../components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../components/data-grid/directive/ava-cell-def.directive';
import { IconComponent } from '../../components/icon/icon.component';
import { AvaTagComponent } from '../../components/tags/tags.component';
import { ButtonComponent } from '../../components/button/button.component';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';

export interface JournalEntry {
  journalId: string;
  date: string;
  journalDescription: string;
  journalStatus: 'Template' | 'Posted' | 'Rejected' | 'Ready to Approve' | 'Draft';
  sourceTransaction: string;
  drCrTotals: string;
  documents?: boolean;
  [key: string]: any; // Allow additional properties for flexibility
}

export interface JournalDataGridConfig {
  showSearch?: boolean;
  showCreateButton?: boolean;
  showFilter?: boolean;
  showSort?: boolean;
  zebraLines?: boolean;
  searchPlaceholder?: string;
  createButtonLabel?: string;
  emptyMessage?: string;
  loadingMessage?: string;
  customColumns?: string[]; // Allow users to specify which columns to show
}

export interface JournalDataGridEvent {
  type: 'create' | 'row-click' | 'document-click' | 'search' | 'sort' | 'filter';
  data?: any;
  row?: JournalEntry;
  searchTerm?: string;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
}

@Component({
  selector: 'ava-journal-data-grid',
  standalone: true,
  imports: [
    CommonModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
    IconComponent,
    AvaTagComponent,
    ButtonComponent,
    AvaTextboxComponent,
  ],
  templateUrl: './journal-data-grid.component.html',
  styleUrl: './journal-data-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class JournalDataGridComponent {
  @Input() config: JournalDataGridConfig = {
    showSearch: true,
    showCreateButton: true,
    showFilter: true,
    showSort: true,
    zebraLines: false,
    searchPlaceholder: 'Search for Journal Entries',
    createButtonLabel: 'Create Journal Entry',
    emptyMessage: 'No journal entries found',
    loadingMessage: 'Loading journal entries...',
    customColumns: ['journalId', 'date', 'journalDescription', 'journalStatus', 'sourceTransaction', 'drCrTotals', 'documents']
  };

  @Input() data: JournalEntry[] = [];
  @Input() loading = false;
  @Input() disabled = false;

  @Output() journalEvent = new EventEmitter<JournalDataGridEvent>();
  @Output() createClick = new EventEmitter<void>();
  @Output() rowClick = new EventEmitter<JournalEntry>();
  @Output() documentClick = new EventEmitter<JournalEntry>();
  @Output() searchChange = new EventEmitter<string>();

  searchTerm = '';

  get displayedColumns(): string[] {
    return this.config.customColumns || [
      'journalId',
      'date', 
      'journalDescription',
      'journalStatus',
      'sourceTransaction',
      'drCrTotals',
      'documents'
    ];
  }

  get filteredData(): JournalEntry[] {
    if (!this.searchTerm) {
      return this.data;
    }

    const searchLower = this.searchTerm.toLowerCase();
    return this.data.filter((entry) =>
      Object.values(entry).some((value) =>
        String(value).toLowerCase().includes(searchLower)
      )
    );
  }

  onCreateJournalEntry() {
    this.createClick.emit();
    this.emitJournalEvent('create');
  }

  onRowClick(row: JournalEntry) {
    this.rowClick.emit(row);
    this.emitJournalEvent('row-click', row);
  }

  onDocumentClick(row: JournalEntry, event: Event) {
    event.stopPropagation(); // Prevent row click
    this.documentClick.emit(row);
    this.emitJournalEvent('document-click', row);
  }

  onSearchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const term = target?.value || '';
    this.searchTerm = term;
    this.searchChange.emit(term);
    this.emitJournalEvent('search', undefined, { searchTerm: term });
  }

  getStatusTagColor(status: string): 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' {
    switch (status) {
      case 'Posted':
        return 'success';
      case 'Template':
        return 'info';
      case 'Rejected':
        return 'error';
      case 'Ready to Approve':
        return 'warning';
      case 'Draft':
        return 'default';
      default:
        return 'default';
    }
  }

  getStatusTagVariant(status: string): 'filled' | 'outlined' {
    // Use outlined variant for all status tags as per the image
    return 'outlined';
  }

  private emitJournalEvent(
    type: JournalDataGridEvent['type'], 
    row?: JournalEntry, 
    additionalData?: Partial<JournalDataGridEvent>
  ) {
    const event: JournalDataGridEvent = {
      type,
      row,
      ...additionalData
    };
    this.journalEvent.emit(event);
  }

  trackByJournalId(index: number, item: JournalEntry): string {
    return item.journalId;
  }
}
