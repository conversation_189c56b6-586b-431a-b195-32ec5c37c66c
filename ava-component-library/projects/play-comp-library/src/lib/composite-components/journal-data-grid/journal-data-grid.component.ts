import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataGridComponent } from '../../components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../components/data-grid/directive/ava-cell-def.directive';
import { ButtonComponent } from '../../components/button/button.component';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';

export interface JournalDataGridConfig {
  showSearch?: boolean;
  showCreateButton?: boolean;
  zebraLines?: boolean;
  searchPlaceholder?: string;
  createButtonLabel?: string;
  emptyMessage?: string;
  loadingMessage?: string;
}

export interface JournalDataGridEvent {
  type: 'create' | 'search' | 'data-sorted';
  data?: any;
  searchTerm?: string;
  sortedData?: any[];
}

@Component({
  selector: 'ava-journal-data-grid',
  standalone: true,
  imports: [
    CommonModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
    ButtonComponent,
    AvaTextboxComponent,
  ],
  templateUrl: './journal-data-grid.component.html',
  styleUrl: './journal-data-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class JournalDataGridComponent {
  @Input() config: JournalDataGridConfig = {
    showSearch: true,
    showCreateButton: true,
    zebraLines: false,
    searchPlaceholder: 'Search entries',
    createButtonLabel: 'Create Entry',
    emptyMessage: 'No entries found',
    loadingMessage: 'Loading entries...'
  };

  @Input() dataSource: any[] = [];
  @Input() displayedColumns: string[] = [];
  @Input() loading = false;
  @Input() disabled = false;

  @Output() journalEvent = new EventEmitter<JournalDataGridEvent>();
  @Output() createClick = new EventEmitter<void>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() dataSorted = new EventEmitter<any[]>();

  searchTerm = '';

  get filteredData(): any[] {
    if (!this.searchTerm) {
      return this.dataSource;
    }

    const searchLower = this.searchTerm.toLowerCase();
    return this.dataSource.filter((entry) =>
      Object.values(entry).some((value) =>
        String(value).toLowerCase().includes(searchLower)
      )
    );
  }

  onCreateClick() {
    this.createClick.emit();
    this.emitJournalEvent('create');
  }

  onSearchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const term = target?.value || '';
    this.searchTerm = term;
    this.searchChange.emit(term);
    this.emitJournalEvent('search', { searchTerm: term });
  }

  onDataSorted(sortedData: any[]) {
    this.dataSorted.emit(sortedData);
    this.emitJournalEvent('data-sorted', { sortedData });
  }

  private emitJournalEvent(
    type: JournalDataGridEvent['type'],
    data?: any
  ) {
    const event: JournalDataGridEvent = {
      type,
      data
    };
    this.journalEvent.emit(event);
  }
}
