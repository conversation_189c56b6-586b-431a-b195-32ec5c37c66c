/* Import data-grid tokens for consistent styling */
@use "../../styles/tokens/components/_data-grid.css";

.ava-journal-data-grid {
  width: 100%;
  font-family: var(--grid-font-family-body);
  color: var(--grid-text-color);

  /* Header Section Styling */
  .journal-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--global-spacing-4);
    gap: var(--global-spacing-4);
    flex-wrap: wrap;

    .header-left {
      flex: 1;
      min-width: 200px;

      .search-input {
        max-width: 400px;
        width: 100%;
      }
    }

    .header-right {
      flex-shrink: 0;

      .create-button {
        white-space: nowrap;
      }
    }

    /* Responsive behavior */
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;

      .header-left,
      .header-right {
        width: 100%;
      }

      .search-input {
        max-width: none;
      }
    }
  }

  /* Data Grid Content */
  .journal-grid-content {
    position: relative;

    /* Custom styling for journal-specific cells */
    .journal-id-link {
      color: var(--color-brand-primary);
      cursor: pointer;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.2s ease;

      &:hover,
      &:focus {
        color: var(--color-brand-primary-hover, var(--color-brand-primary));
        text-decoration: underline;
      }

      &:focus {
        outline: 2px solid var(--color-brand-primary);
        outline-offset: 2px;
        border-radius: 2px;
      }
    }

    .journal-description {
      display: block;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .status-tag {
      /* Status tags inherit their styling from the tag component */
      /* Custom positioning if needed */
      display: inline-flex;
    }

    .totals-amount {
      font-weight: 500;
      font-variant-numeric: tabular-nums;
      text-align: right;
      display: block;
    }

    .documents-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 24px;

      .document-icon {
        transition: color 0.2s ease, transform 0.2s ease;

        &:hover,
        &:focus {
          color: var(--color-brand-primary) !important;
          transform: scale(1.1);
        }

        &:focus {
          outline: 2px solid var(--color-brand-primary);
          outline-offset: 2px;
          border-radius: 2px;
        }
      }

      .no-documents {
        color: var(--grid-text-disabled);
        font-size: 14px;
      }
    }

    /* Empty State Styling */
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--global-spacing-8) var(--global-spacing-4);
      text-align: center;
      min-height: 200px;

      .empty-icon {
        margin-bottom: var(--global-spacing-4);
        opacity: 0.6;
      }

      .empty-message {
        color: var(--grid-text-disabled);
        font-size: 16px;
        margin: 0;
      }
    }

    /* Loading State Styling */
    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--global-spacing-8) var(--global-spacing-4);
      text-align: center;
      min-height: 200px;

      .loading-icon {
        margin-bottom: var(--global-spacing-4);
        animation: spin 1s linear infinite;
      }

      .loading-message {
        color: var(--grid-text-color);
        font-size: 16px;
        margin: 0;
      }
    }
  }

  /* Override data-grid specific styles for journal entries */
  ava-data-grid {
    .ava-data-table-wrapper {
      .data-table-wrapper {
        .ava-data-table {
          /* Ensure proper column alignment */
          th,
          td {
            &:first-child {
              /* Journal ID column */
              min-width: 120px;
            }

            &:nth-child(2) {
              /* Date column */
              min-width: 100px;
            }

            &:nth-child(3) {
              /* Description column */
              min-width: 200px;
              max-width: 300px;
            }

            &:nth-child(4) {
              /* Status column */
              min-width: 140px;
            }

            &:nth-child(5) {
              /* Source Transaction column */
              min-width: 120px;
            }

            &:nth-child(6) {
              /* Dr/Cr Totals column */
              min-width: 120px;
              text-align: right;
            }

            &:last-child {
              /* Documents column */
              width: 80px;
              text-align: center;
            }
          }
        }
      }
    }
  }
}

/* Keyframe animation for loading spinner */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .ava-journal-data-grid {
    .loading-icon {
      animation: none;
    }

    .document-icon {
      transition: none;
    }

    .journal-id-link {
      transition: none;
    }
  }
}
