/* Import data-grid tokens for consistent styling */
@use "../../styles/tokens/components/_data-grid.css";

.ava-journal-data-grid {
  width: 100%;
  font-family: var(--grid-font-family-body);
  color: var(--grid-text-color);

  /* Header Section - Minimal structural styling only */
  .journal-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--grid-cell-paading, 1rem);
    gap: var(--grid-cell-paading, 1rem);
    flex-wrap: wrap;

    .header-left {
      flex: 1;
      min-width: 200px;

      .search-input {
        max-width: 400px;
        width: 100%;
      }
    }

    .header-right {
      flex-shrink: 0;

      .create-button {
        white-space: nowrap;
      }
    }

    /* Responsive behavior */
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;

      .header-left,
      .header-right {
        width: 100%;
      }

      .search-input {
        max-width: none;
      }
    }
  }

  /* Data Grid Content - Minimal styling */
  .journal-grid-content {
    position: relative;

    /* Empty State Styling */
    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--grid-cell-paading, 1rem);
      text-align: center;
      min-height: 200px;

      .empty-message {
        color: var(--grid-text-disabled);
        margin: 0;
      }
    }

    /* Loading State Styling */
    .loading-state {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--grid-cell-paading, 1rem);
      text-align: center;
      min-height: 200px;

      .loading-message {
        color: var(--grid-text-color);
        margin: 0;
      }
    }
  }
}

/* Keyframe animation for loading spinner */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .ava-journal-data-grid {
    .loading-icon {
      animation: none;
    }

    .document-icon {
      transition: none;
    }

    .journal-id-link {
      transition: none;
    }
  }
}
