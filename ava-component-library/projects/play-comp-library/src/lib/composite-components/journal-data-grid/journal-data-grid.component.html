<div class="ava-journal-data-grid">
  <!-- Header Section with Search and Create Button -->
  <div class="journal-grid-header">
    <div class="header-left">
      <ava-textbox
        *ngIf="config.showSearch"
        [placeholder]="config.searchPlaceholder || 'Search for Journal Entries'"
        [disabled]="disabled"
        icon="search"
        iconPosition="start"
        (textboxChange)="onSearchChange($event)"
        class="search-input"
      >
      </ava-textbox>
    </div>
    
    <div class="header-right">
      <ava-button
        *ngIf="config.showCreateButton"
        [label]="config.createButtonLabel || 'Create Journal Entry'"
        variant="primary"
        size="medium"
        iconName="plus"
        [iconSize]="16"
        [disabled]="disabled"
        (userClick)="onCreateJournalEntry()"
        class="create-button"
      >
      </ava-button>
    </div>
  </div>

  <!-- Data Grid Section -->
  <div class="journal-grid-content">
    <ava-data-grid 
      [dataSource]="filteredData" 
      [displayedColumns]="displayedColumns"
      [zerbaLine]="config.zebraLines || false"
    >
      <!-- Journal ID Column -->
      <ng-container 
        avaColumnDef="journalId" 
        [sortable]="config.showSort || true"
        [filter]="config.showFilter || false"
      >
        <ng-container *avaHeaderCellDef>Journal ID</ng-container>
        <ng-container *avaCellDef="let row">
          <span 
            class="journal-id-link" 
            (click)="onRowClick(row)"
            [attr.tabindex]="0"
            [attr.role]="'button'"
            (keyup.enter)="onRowClick(row)"
          >
            {{ row.journalId }}
          </span>
        </ng-container>
      </ng-container>

      <!-- Date Column -->
      <ng-container 
        avaColumnDef="date" 
        [sortable]="config.showSort || true"
        [filter]="config.showFilter || false"
      >
        <ng-container *avaHeaderCellDef>Date</ng-container>
        <ng-container *avaCellDef="let row">
          {{ row.date }}
        </ng-container>
      </ng-container>

      <!-- Journal Description Column -->
      <ng-container 
        avaColumnDef="journalDescription" 
        [sortable]="config.showSort || true"
        [filter]="config.showFilter || false"
      >
        <ng-container *avaHeaderCellDef>Journal Description</ng-container>
        <ng-container *avaCellDef="let row">
          <span class="journal-description">{{ row.journalDescription }}</span>
        </ng-container>
      </ng-container>

      <!-- Journal Status Column with Outline Tags -->
      <ng-container 
        avaColumnDef="journalStatus" 
        [sortable]="config.showSort || true"
        [filter]="config.showFilter || false"
      >
        <ng-container *avaHeaderCellDef>Journal Status</ng-container>
        <ng-container *avaCellDef="let row">
          <ava-tag
            [label]="row.journalStatus"
            [color]="getStatusTagColor(row.journalStatus)"
            [variant]="getStatusTagVariant(row.journalStatus)"
            size="sm"
            class="status-tag"
          >
          </ava-tag>
        </ng-container>
      </ng-container>

      <!-- Source Transaction Column -->
      <ng-container 
        avaColumnDef="sourceTransaction" 
        [sortable]="config.showSort || true"
        [filter]="config.showFilter || false"
      >
        <ng-container *avaHeaderCellDef>Source Transaction</ng-container>
        <ng-container *avaCellDef="let row">
          {{ row.sourceTransaction }}
        </ng-container>
      </ng-container>

      <!-- Dr/Cr Totals Column -->
      <ng-container 
        avaColumnDef="drCrTotals" 
        [sortable]="config.showSort || true"
        [filter]="config.showFilter || false"
      >
        <ng-container *avaHeaderCellDef>Dr/Cr Totals</ng-container>
        <ng-container *avaCellDef="let row">
          <span class="totals-amount">{{ row.drCrTotals }}</span>
        </ng-container>
      </ng-container>

      <!-- Documents Column with Icon -->
      <ng-container 
        avaColumnDef="documents" 
        [sortable]="false"
        [filter]="false"
      >
        <ng-container *avaHeaderCellDef>Documents</ng-container>
        <ng-container *avaCellDef="let row">
          <div class="documents-cell">
            <ava-icon
              *ngIf="row.documents"
              iconName="file-text"
              [iconSize]="16"
              iconColor="var(--color-text-secondary)"
              [cursor]="true"
              (userClick)="onDocumentClick(row, $event)"
              class="document-icon"
              [attr.tabindex]="0"
              [attr.role]="'button'"
              [attr.aria-label]="'View documents for journal ' + row.journalId"
              (keyup.enter)="onDocumentClick(row, $event)"
            >
            </ava-icon>
            <span *ngIf="!row.documents" class="no-documents">—</span>
          </div>
        </ng-container>
      </ng-container>
    </ava-data-grid>

    <!-- Empty State -->
    <div *ngIf="!loading && filteredData.length === 0" class="empty-state">
      <ava-icon 
        iconName="file-x" 
        [iconSize]="48"
        iconColor="var(--color-text-disabled)"
        class="empty-icon"
      >
      </ava-icon>
      <p class="empty-message">{{ config.emptyMessage || 'No journal entries found' }}</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="loading-state">
      <ava-icon 
        iconName="loader" 
        [iconSize]="24"
        iconColor="var(--color-brand-primary)"
        class="loading-icon"
      >
      </ava-icon>
      <p class="loading-message">{{ config.loadingMessage || 'Loading journal entries...' }}</p>
    </div>
  </div>
</div>
