<div class="ava-journal-data-grid">
  <!-- Header Section with Search and Create Button -->
  <div class="journal-grid-header" *ngIf="config.showSearch || config.showCreateButton">
    <div class="header-left">
      <ava-textbox
        *ngIf="config.showSearch"
        [placeholder]="config.searchPlaceholder || 'Search entries'"
        [disabled]="disabled"
        icon="search"
        iconPosition="start"
        (textboxChange)="onSearchChange($event)"
        class="search-input"
      >
      </ava-textbox>
    </div>

    <div class="header-right">
      <ava-button
        *ngIf="config.showCreateButton"
        [label]="config.createButtonLabel || 'Create Entry'"
        variant="primary"
        size="medium"
        iconName="plus"
        [iconSize]="16"
        [disabled]="disabled"
        (userClick)="onCreateClick()"
        class="create-button"
      >
      </ava-button>
    </div>
  </div>

  <!-- Data Grid Section -->
  <div class="journal-grid-content">
    <ava-data-grid
      [dataSource]="filteredData"
      [displayedColumns]="displayedColumns"
      [zerbaLine]="config.zebraLines || false"
      (dataSorted)="onDataSorted($event)"
    >
      <!-- Allow users to define their own columns via ng-content -->
      <ng-content></ng-content>
    </ava-data-grid>

    <!-- Empty State -->
    <div *ngIf="!loading && filteredData.length === 0" class="empty-state">
      <p class="empty-message">{{ config.emptyMessage || 'No entries found' }}</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="loading-state">
      <p class="loading-message">{{ config.loadingMessage || 'Loading entries...' }}</p>
    </div>
  </div>
</div>
