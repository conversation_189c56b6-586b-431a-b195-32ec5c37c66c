:root {

    --select-place-holder-color: var(--color-text-placeholder);
    --select-place-holder-font-size: var(--font-body-2);
    --select-option-padding: var(--global-spacing-3) var(--global-spacing-4);
    --select-option-selected: var(--color-text-on-primary);
    --select-text: var(--color-text-primary);
    --select-brand-color: var(--color-brand-primary);
    --select-brand-text-color: var(--color-text-on-primary);
    --select-search-font: var(--font-body-2);
    --select-font: var(--font-body-2);

    --select-menu-padding: var(--global-spacing-2);
    --select-disabled-text: var(--color-text-disabled);
    --select-line-height: var(--global-line-height-normal);
    --select-toggle-padding: var(--global-spacing-3) var(--global-spacing-4);
    --select-option-padding: var(--global-spacing-3) var(--global-spacing-3);
    --select-box-shadow: var(--global-elevation-01);
    --select-toggle-background: var(--color-background-primary);
    --select-toggle-border-radius: var(--global-radius-sm);
    --select-menu-background: var(--dropdown-background);
    --select-menu-border: var(--color-border-default);
    --select-menu-border-radius: var(--global-radius-sm);
    --select-menu-shadow: var(--global-elevation-02);
    --select-menu-padding: var(--global-spacing-2);
    --select-toggle-font: var(--dropdown-font);
    --select-item-transition: var(--motion-pattern-fade);
    --select-item-font: var(--dropdown-font);
    --select-item-text: var(--select-text);
    --select-size-md-height: 40px;
    --select-item-background: transparent;
    --select-item-background-hover: var(--color-surface-subtle-hover);
    --select-item-text-hover: var(--color-text-primary);
    --select-search-background: var(--color-background-primary);

    /* Label */
    --select-label-font: var(--font-label);
    --select-label-color: var(--color-text-primary);
    --select-label-margin: var(--global-spacing-3);
    --select-label-weight: 500;
    --select-required-color: var(--color-text-error);
    /*error*/
    --select-error-border: var(--color-border-error);
    --select-error-gap: var(--global-spacing-1);
    --select-error-color: var(--color-text-error);
    --select-error-font-size: 0.875rem;
    --select-search-border-radius: var(--global-radius-sm);

    /* --- Select Sizes --- */
    /* Input Sizes */
    --select-input-padding-sm: 0.5rem 0.625rem;
    --select-input-min-height-sm: 1.7rem;
    --select-input-font-size-sm: 0.875rem;
    --select-input-padding-md: 0.75rem 0.75rem;
    --select-input-min-height-md: 2.2rem;
    --select-input-font-size-md: 0.875rem;
    --select-input-padding-lg: 0.85rem 1rem;
    --select-input-min-height-lg: 2.5rem;
    --select-input-font-size-lg: 1.05rem;

}