$calendar-input-width-single: 16.5rem;
$calendar-input-width-range: 21.75rem;
$calendar-input-height: 3rem;

// Calendar Navigation
$calendar-nav-button-height-width: 2.25rem;
$calendar-nav-button-padding: var(--calendar-size-sm-padding);
$calendar-nav-controls-gap: 0.25rem;
$calendar-nav-controls-gap-large: var(--calendar-size-sm-padding);

// Calendar Input Segments
$calendar-segment-day-month-width: 1.5rem;
$calendar-segment-year-width: 2.5rem;
$calendar-input-icon-right: 1rem;
$calendar-input-icon-padding: 0.375rem;

// Calendar Shadows
$calendar-nav-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.1);
$calendar-nav-shadow-hover: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.15);

$calendar-range-pill-radius: 1.5em;


.date-picker {
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-sm-font);
  width: 300px;
  position: relative;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--calendar-size-sm-padding);
}

.input-group {
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.structured-input {
  flex: 1 1 auto;
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
  padding: var(--calendar-input-padding) 2.5rem var(--calendar-input-padding) var(--calendar-input-padding);
  border: var(--calendar-input-border);
  border-radius: var(--calendar-input-border-radius);
  background: var(--calendar-input-background);
  cursor: text;
  width: $calendar-input-width-single;
  height: $calendar-input-height;
  transition: all 0.2s ease;
  font-size: inherit;
}

.range-structured {
  width: $calendar-input-width-range;
  height: $calendar-input-height;
  gap: var(--calendar-size-sm-padding);
}

.date-part {
  display: flex;
  align-items: center;
}

.date-segment {
  border: none;
  outline: none;
  background: transparent;
  font-size: inherit;
  font-family: inherit;
  text-align: center;
  transition: background-color 0.2s ease;
}

.day-segment,
.month-segment {
  width: $calendar-segment-day-month-width;
}

.year-segment {
  width: $calendar-segment-year-width;
}

.separator {
  user-select: none;
}

.range-separator {
  user-select: none;
}

.input-btn {
  flex: 0 0 auto;
  margin-left: 8px;
  /* Ensures the icon is at the right end */
  position: absolute;
  right: $calendar-input-icon-right;
  background: var(--calendar-icon-background);
  border: none;
  cursor: pointer;
  padding: $calendar-input-icon-padding;
  border-radius: var(--calendar-icon-border-radius);
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--calendar-nav-button-hover-background);
  }
}

.calendar-popup {
  width: 300px;
  min-width: 300px;
  box-sizing: border-box;
  position: absolute;
  margin-top: 5px;
  z-index: var(--calendar-popup-z-index);
  background: var(--calendar-popup-background);
  border: var(--calendar-popup-border);
  border-radius: var(--calendar-popup-border-radius);
  box-shadow: var(--calendar-popup-shadow);
  padding: $calendar-input-icon-right;
  width: 300px;
  height: auto;
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.calendar-popup.surface-glass {
  background: rgba(255, 255, 255, 0.4); // fallback, will be overridden by variant
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--calendar-popup-border-radius, 16px);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  color: #222;
}
.calendar-popup.surface-glass.medium {
  background: rgba(255, 255, 255, 0.5);
}
.calendar-popup.surface-glass.strong {
  background: rgba(255, 255, 255, 0.75);
}
.calendar-popup.surface-glass.max {
  background: rgba(255, 255, 255, 1.0);
}


.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-controls {
  display: flex;
  gap: $calendar-nav-controls-gap-large;
}

.month-year-display {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.month-selector,
.year-selector {
  padding: 0.5rem 0;
  background: transparent;
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-md-font);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  border: none;
  outline: none;
}

.month-selector {
  color: var(--calendar-month-text);
}

.year-selector {
  color: var(--calendar-year-text);
}

.month-selector.selected {
  color: var(--calendar-nav-button-background);
}

.year-selector.selected {
  color: var(--calendar-nav-button-background);
}

.month-selector:not(.selected) {
  color: var(--calendar-year-text);
}

.year-selector:not(.selected) {
  color: var(--calendar-year-text);
}

.month-selector:hover,
.year-selector:hover {
  opacity: 0.8;
}

.nav-controls {
  display: flex;
  gap: $calendar-nav-controls-gap;
}

.nav-btn {
  background-color: white;
  border: none;
  cursor: pointer;
  padding: $calendar-nav-button-padding;
  border-radius: 50%;
  transition: all 0.2s ease;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: $calendar-nav-button-height-width;
  height: $calendar-nav-button-height-width;
  box-shadow: $calendar-nav-shadow;

  &:hover {
    background: var(--calendar-nav-button-background);
    box-shadow: $calendar-nav-shadow-hover;
  }
}

.calendar-grid {
  margin-top: 0.5rem;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--calendar-size-sm-padding);
  margin-bottom: var(--calendar-size-sm-padding);
}

.weekday {
  height: auto;
  text-align: center;
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-sm-font);
  color: var(--calendar-day-name-text);
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Replace the existing range-related styles in your SCSS file with this:

.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--calendar-size-sm-padding);
}

.day {
  aspect-ratio: 1 / 1; // always square, responsive
  width: 100%;         // fill the grid cell
  height: auto;        // let aspect-ratio control height
  padding: 0;          // remove extra padding for perfect shape
  text-align: center;
  cursor: pointer;
  border: none;
  border-radius: var(--calendar-cell-border-radius);
  background: none;
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-sm-font);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); // slow, smooth animation
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--calendar-date-text);

  &.circle-selector {
    border-radius: 50% !important;
  }

  &.square-selector {
    border-radius: 0.5rem !important; // or 0 for perfect square
  }

  &:hover {
    background: var(--calendar-date-hover-background);
    color: var(--calendar-date-selected-text);
  }

  &.other-month {
    color: var(--calendar-date-disabled-text);
  }

  &.today {
    border: var(--calendar-date-today-border);
  }

  &.selected {
    background: var(--calendar-date-selected-background);
    color: var(--calendar-date-selected-text);
  }
}

// Range styling - properly curved like the requirement
.day.in-range {
  background: var(--calendar-date-hover-background);
  color: var(--calendar-range-start-text);
  border-radius: var(--calendar-cell-border-radius);
  position: relative;
  z-index: 1;
}

.day.range-start,
.day.range-end {
  background: var(--calendar-range-start-background) !important;
  color: var(--calendar-range-start-text) !important;
  border-radius: var(--calendar-cell-border-radius) !important;
  position: relative;
  z-index: 2;
}

// Handle adjacent start/end dates (no gap)
.day.range-start.range-end {
  border-radius: var(--calendar-cell-border-radius) !important;
  background: var(--calendar-range-start-background) !important;
  color: var(--calendar-range-start-text) !important;
}

// Remove the continuous background - keep individual rounded cells
.day.in-range::before,
.day.range-start::before,
.day.range-end::before {
  display: none;
}

// Single date range (start and end are the same)
.day.range-start.range-end::before {
  display: none;
}

// Handle first column (Sunday) range styling
.days .day.in-range:nth-child(7n+1)::before,
.days .day.range-start:nth-child(7n+1)::before {
  left: 0;
}

// Handle last column (Saturday) range styling
.days .day.in-range:nth-child(7n)::before,
.days .day.range-end:nth-child(7n)::before {
  right: 0;
}

// Today styling adjustments for range dates
.day.today.in-range,
.day.today.range-start,
.day.today.range-end {
  border: none !important;
  box-shadow: none !important;
}

// Ensure today border shows for single selected dates
.day.today:not(.in-range):not(.range-start):not(.range-end) {
  border: var(--calendar-date-today-border);
}

// Override hover effects for range dates
.day.range-start:hover,
.day.range-end:hover {
  background: var(--calendar-range-start-background) !important;
  color: var(--calendar-range-start-text) !important;
}

.day.in-range:hover {
  background: var(--calendar-date-hover-background);
  color: var(--calendar-range-start-text);
}