.custom-dialog {
    display: flex;
    align-items: center;
    flex-direction: column;
    flex: 1;
    gap: 16px;
    text-align: center;

    .icon-container {
        text-align: center;
        margin-bottom: 8px;
    }

    .popup-title {
        margin: 0;
        font-size: var(--popup-heading-size);
        font-weight: var(--popup-heading-weight);
        line-height: var(--popup-heading-line-height);
        color: var(--popup-heading-text);

        &.success {
            color: var(--color-text-success);
        }

        &.error {
            color: var(--color-text-error);
        }

        &.warning {
            color: var(--color-text-warning);
        }

        &.info {
            color: var(--color-text-info);
        }
    }

    .inline-text {
        color: var(--popup-black-color);
        font-size: var(--popup-description-size);
        line-height: var(--popup-description-line-height);
        margin: 0;
        max-width: 400px;
    }

    .custom-content {
        color: var(--popup-black-color);
        font-size: var(--popup-description-size);
        line-height: var(--popup-description-line-height);
        max-width: 400px;
        
        // Allow custom styling within content
        * {
            max-width: 100%;
        }
    }

    .dialog-actions {
        display: flex;
        gap: var(--global-spacing-3);
        margin-top: var(--global-spacing-2);
        justify-content: center;
        flex-wrap: wrap;
    }

    // Variant-specific styling
    &.success {
        .icon-container {
            color: var(--color-text-success, #22c55e);
        }
    }

    &.error {
        .icon-container {
            color: var(--color-text-error, #dc2626);
        }
    }

    &.warning {
        .icon-container {
            color: var(--color-text-warning, #f59e0b);
        }
    }

    &.info {
        .icon-container {
            color: var(--color-text-info, #3b82f6);
        }
    }
}
