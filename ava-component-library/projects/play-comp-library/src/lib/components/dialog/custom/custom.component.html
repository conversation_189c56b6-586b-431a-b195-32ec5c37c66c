<div class="custom-dialog" [ngClass]="variant">
    <div class="icon-container" *ngIf="showIcon && (icon || variant !== 'default')">
        <ava-icon 
            [iconName]="variantIcon" 
            [iconSize]="iconSize" 
            [iconColor]="variantIconColor" 
            aria-hidden="true">
        </ava-icon>
    </div>
    
    <h3 class="popup-title" 
        [ngClass]="variant" 
        id="popup-title" 
        *ngIf="showTitle && title">
        {{ title }}
    </h3>
    
    <p class="inline-text" 
       *ngIf="showMessage && message" 
       [innerHTML]="message">
    </p>
    
    <div class="custom-content" 
         *ngIf="customContent" 
         [innerHTML]="customContent">
    </div>
    
    <!-- Slot for projected content -->
    <ng-content></ng-content>
    
    <div class="dialog-actions" *ngIf="buttons && buttons.length > 0">
        <ava-button 
            *ngFor="let button of buttons; trackBy: trackByButton"
            [label]="button.label" 
            [variant]="button.variant || 'secondary'" 
            size="medium"
            [disabled]="button.disabled || false"
            (userClick)="onButtonClick(button)">
        </ava-button>
    </div>
</div>
