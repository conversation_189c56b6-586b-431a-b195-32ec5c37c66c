<div class="info-dialog">
    <div class="icon-container">
        <ava-icon 
            [iconName]="icon" 
            [iconSize]="iconSize" 
            [iconColor]="iconColor" 
            aria-hidden="true">
        </ava-icon>
    </div>
    
    <h3 class="popup-title" id="popup-title">
        {{ title }}
    </h3>
    
    <p class="inline-text" [innerHTML]="message"></p>
    
    <div class="dialog-actions" *ngIf="showOkButton || showLearnMoreButton">
        <ava-button 
            *ngIf="showLearnMoreButton"
            [label]="learnMoreButtonText" 
            variant="secondary" 
            size="medium"
            (userClick)="onLearnMore()">
        </ava-button>
        
        <ava-button 
            *ngIf="showOkButton"
            [label]="okButtonText" 
            variant="primary" 
            size="medium"
            (userClick)="onOk()">
        </ava-button>
    </div>
</div>
