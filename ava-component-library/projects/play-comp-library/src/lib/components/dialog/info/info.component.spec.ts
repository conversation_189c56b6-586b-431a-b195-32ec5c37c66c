import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InfoComponent } from './info.component';

describe('InfoComponent', () => {
  let component: InfoComponent;
  let fixture: ComponentFixture<InfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InfoComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(InfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display default info message', () => {
    expect(component.message).toBe('Here is some important information for you.');
  });

  it('should display default info title', () => {
    expect(component.title).toBe('Information');
  });

  it('should use info icon by default', () => {
    expect(component.icon).toBe('info');
  });
});
