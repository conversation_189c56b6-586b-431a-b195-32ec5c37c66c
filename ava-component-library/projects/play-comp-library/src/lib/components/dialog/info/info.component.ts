import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';

@Component({
  selector: 'ava-info',
  imports: [CommonModule, IconComponent, ButtonComponent],
  templateUrl: './info.component.html',
  styleUrl: './info.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class InfoComponent {
  @Input() title = 'Information';
  @Input() message = 'Here is some important information for you.';
  @Input() icon = 'info';
  @Input() iconColor = 'var(--color-text-info)';
  @Input() iconSize = 50;
  @Input() showOkButton = true;
  @Input() okButtonText = 'OK';
  @Input() showLearnMoreButton = false;
  @Input() learnMoreButtonText = 'Learn More';

  @Output() closed = new EventEmitter<{ action: string }>();

  onOk() {
    this.closed.emit({ action: 'ok' });
  }

  onLearnMore() {
    this.closed.emit({ action: 'learn-more' });
  }
}
