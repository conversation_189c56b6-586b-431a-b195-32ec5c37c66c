import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ConfirmationComponent } from './confirmation.component';

describe('ConfirmationComponent', () => {
  let component: ConfirmationComponent;
  let fixture: ComponentFixture<ConfirmationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ConfirmationComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ConfirmationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display default confirmation message', () => {
    expect(component.message).toBe('Are you sure you want to proceed with this action?');
  });

  it('should display default confirmation title', () => {
    expect(component.title).toBe('Confirm Action');
  });

  it('should use help-circle icon by default', () => {
    expect(component.icon).toBe('help-circle');
  });

  it('should use error variant for destructive actions', () => {
    component.destructive = true;
    expect(component.effectiveConfirmVariant).toBe('error');
  });
});
