import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';

@Component({
  selector: 'ava-success',
  imports: [CommonModule, IconComponent],
  templateUrl: './success.component.html',
  styleUrl: './success.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SuccessComponent {
  @Input() title = 'Success';
  @Input() message = 'Operation completed successfully!';
  @Input() icon = 'circle-check';
  @Input() iconColor = 'var(--color-text-success)';
  @Input() iconSize = 50;

  @Output() closed = new EventEmitter<{ action: string }>();

  onClose() {
    this.closed.emit({ action: 'close' });
  }
}
