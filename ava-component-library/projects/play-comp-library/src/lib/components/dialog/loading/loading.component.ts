import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { ButtonComponent } from '../../button/button.component';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'ava-loading',
  imports: [ButtonComponent, CommonModule],
  templateUrl: './loading.component.html',
  styleUrl: './loading.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoadingComponent {
  @Input() title = 'Loading...';
  @Input() message = 'Please wait while we process your request.';
  @Input() showProgress = false;
  @Input() progress = 0; // 0-100
  @Input() showCancelButton = false;
  @Input() cancelButtonText = 'Cancel';
  @Input() spinnerColor = 'var(--color-brand-primary)';
  @Input() indeterminate = true; // For indeterminate progress

  get progressPercentage(): string {
    return Math.min(Math.max(this.progress, 0), 100) + '%';
  }

  get progressText(): string {
    if (this.indeterminate) {
      return '';
    }
    return `${Math.round(this.progress)}%`;
  }

  @Output() closed = new EventEmitter<{ action: string }>();

  onCancel() {
    this.closed.emit({ action: 'cancel' });
  }
}
