import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LoadingComponent } from './loading.component';

describe('LoadingComponent', () => {
  let component: LoadingComponent;
  let fixture: ComponentFixture<LoadingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LoadingComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(LoadingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display default loading message', () => {
    expect(component.message).toBe('Please wait while we process your request.');
  });

  it('should display default loading title', () => {
    expect(component.title).toBe('Loading...');
  });

  it('should calculate progress percentage correctly', () => {
    component.progress = 75;
    expect(component.progressPercentage).toBe('75%');
  });

  it('should clamp progress values', () => {
    component.progress = 150;
    expect(component.progressPercentage).toBe('100%');
    
    component.progress = -10;
    expect(component.progressPercentage).toBe('0%');
  });
});
