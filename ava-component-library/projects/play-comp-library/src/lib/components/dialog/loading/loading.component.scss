.loading-dialog {
    display: flex;
    align-items: center;
    flex-direction: column;
    flex: 1;
    gap: 16px;
    text-align: center;

    .spinner-container {
        text-align: center;
        margin-bottom: 8px;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid var(--color-background-subtle);
        border-top: 4px solid var(--color-brand-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .popup-title {
        color: var(--popup-heading-text);
        margin: 0;
        font-size: var(--popup-heading-size);
        font-weight: var(--popup-heading-weight);
        line-height: var(--popup-heading-line-height);
    }

    .inline-text {
        color: var(--popup-black-color);
        font-size: var(--popup-description-size);
        line-height: var(--popup-description-line-height);
        margin: 0;
        max-width: 400px;
    }

    .progress-container {
        width: 100%;
        max-width: 300px;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background-color: var(--color-background-subtle);
        border-radius: 4px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background-color: var(--color-brand-primary);
        transition: width 0.3s ease;
        border-radius: 4px;
    }

    .progress-text {
        font-size: 14px;
        color: var(--popup-black-color);
        text-align: center;
    }

    .dialog-actions {
        display: flex;
        gap: 12px;
        margin-top: 8px;
        justify-content: center;
        flex-wrap: wrap;
    }
}
