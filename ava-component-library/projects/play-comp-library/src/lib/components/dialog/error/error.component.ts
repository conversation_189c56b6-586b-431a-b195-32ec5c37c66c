import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';

@Component({
  selector: 'ava-error',
  imports: [CommonModule, IconComponent, ButtonComponent],
  templateUrl: './error.component.html',
  styleUrl: './error.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ErrorComponent {
  @Input() title = 'Error';
  @Input() message = 'An error occurred. Please try again.';
  @Input() icon = 'alert-circle';
  @Input() iconColor = 'var(--color-text-error)';
  @Input() iconSize = 50;
  @Input() showRetryButton = false;
  @Input() retryButtonText = 'Try Again';
  @Input() showCloseButton = true;
  @Input() closeButtonText = 'Close';

  @Output() closed = new EventEmitter<{ action: string }>();

  onRetry() {
    this.closed.emit({ action: 'retry' });
  }

  onClose() {
    this.closed.emit({ action: 'close' });
  }
}
