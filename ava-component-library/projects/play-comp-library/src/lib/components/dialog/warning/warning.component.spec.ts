import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WarningComponent } from './warning.component';

describe('WarningComponent', () => {
  let component: WarningComponent;
  let fixture: ComponentFixture<WarningComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WarningComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(WarningComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display default warning message', () => {
    expect(component.message).toBe('Please review the following information carefully.');
  });

  it('should display default warning title', () => {
    expect(component.title).toBe('Warning');
  });

  it('should use alert-triangle icon by default', () => {
    expect(component.icon).toBe('alert-triangle');
  });
});
