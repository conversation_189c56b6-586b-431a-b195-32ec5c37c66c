import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';

@Component({
  selector: 'ava-warning',
  imports: [CommonModule, IconComponent, ButtonComponent],
  templateUrl: './warning.component.html',
  styleUrl: './warning.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WarningComponent {
  @Input() title = 'Warning';
  @Input() message = 'Please review the following information carefully.';
  @Input() icon = 'alert-triangle';
  @Input() iconColor = 'var(--color-text-warning)';
  @Input() iconSize = 50;
  @Input() showProceedButton = false;
  @Input() proceedButtonText = 'Proceed';
  @Input() showCancelButton = true;
  @Input() cancelButtonText = 'Cancel';

  @Output() closed = new EventEmitter<{ action: string }>();

  onProceed() {
    this.closed.emit({ action: 'proceed' });
  }

  onCancel() {
    this.closed.emit({ action: 'cancel' });
  }
}
