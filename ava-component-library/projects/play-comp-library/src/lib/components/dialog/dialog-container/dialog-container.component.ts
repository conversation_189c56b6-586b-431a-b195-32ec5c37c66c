import { ChangeDetectionStrategy, Component, EventEmitter, Output, ViewChild, ViewContainerRef, ViewEncapsulation } from '@angular/core';
import { IconComponent } from '../../icon/icon.component';

@Component({
  selector: 'ava-dialog-container',
  imports: [IconComponent],
  templateUrl: './dialog-container.component.html',
  styleUrl: './dialog-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class DialogContainerComponent {
  @ViewChild('container', { read: ViewContainerRef, static: true })
  container!: ViewContainerRef;

  @Output() closed = new EventEmitter<any>();

  onBackdropClick() {
    this.closed.emit(null); // send null or any value
  }

  onCloseClick() {
    this.closed.emit({ action: 'close' });
  }
}
