/* ava-dialog-container.component.scss */
.ava-dialog-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
    overflow: hidden;
    overscroll-behavior: contain;
}

.ava-dialog-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: var(--popup-content-padding-xl);
    border: var(--popup-border);
    border-radius: var(--popup-border-radius);
    z-index: 1000;
    min-width: 400px;

    .title {
        font-size: var(--popup-heading-size);
        font-weight: var(--popup-heading-weight);
        margin: var(--popup-heading-margin);
        color: var(--popup-heading-text);
        line-height: var(--popup-heading-line-height);
        text-align: center;
    }

    .inline-text {
        font-size: var(--popup-description-size);
        color: var(--popup-black-color);
        line-height: var(--popup-description-line-height);
        word-break: break-word;
        overflow-wrap: anywhere;
        flex: 1;
        display: block;
        margin: 0;
    }

    .close-container {
        position: relative;

        ava-icon {
            position: absolute;
            top: -13px;
            right: -13px;
        }
    }
}