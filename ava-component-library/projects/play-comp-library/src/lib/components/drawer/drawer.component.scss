/* ===================================================================
   AVA DRAWER COMPONENT STYLES

   Uses Play+ Design System tokens from _base.css and _drawer.css
   All styling references semantic tokens for consistent theming
   =================================================================== */

/* ===================================================================
   SMOOTH SLIDE ANIMATIONS
   =================================================================== */
@keyframes slideFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Prevent body scroll when drawer is open */
:global(body.ava-drawer-open) {
  overflow: hidden;
}

/* ===================================================================
   OVERLAY STYLES
   =================================================================== */
.ava-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--popup-overlay-background, rgba(0, 0, 0, 0.5));
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s cubic-bezier(0.16, 1, 0.3, 1),
              visibility 0.25s cubic-bezier(0.16, 1, 0.3, 1);

  &--open {
    opacity: 1;
    visibility: visible;
  }
}

/* ===================================================================
   ANIMATION WRAPPER
   =================================================================== */
.ava-drawer__animation-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* ===================================================================
   DRAWER BASE STYLES
   =================================================================== */
.ava-drawer {
  position: fixed;
  background: var(--drawer-background);
  border: var(--drawer-border);
  box-shadow: var(--drawer-shadow);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: var(--drawer-z-index);

  /* ===================================================================
     POSITION VARIANTS
     =================================================================== */
  
  /* Right Position (Default) */
  &--right {
    top: 0;
    right: 0;
    bottom: 0;
    border-left: var(--drawer-border);
    border-right: none;

    &.ava-drawer--open {
      .ava-drawer__animation-wrapper.ava-drawer--animated {
        animation: slideFromRight 0.25s cubic-bezier(0.16, 1, 0.3, 1);
      }
    }
  }

  /* Left Position */
  &--left {
    top: 0;
    left: 0;
    bottom: 0;
    border-right: var(--drawer-border);
    border-left: none;

    &.ava-drawer--open {
      .ava-drawer__animation-wrapper.ava-drawer--animated {
        animation: slideFromLeft 0.25s cubic-bezier(0.16, 1, 0.3, 1);
      }
    }
  }

  /* Top Position */
  &--top {
    top: 0;
    left: 0;
    right: 0;
    border-bottom: var(--drawer-border);
    border-top: none;

    &.ava-drawer--open {
      .ava-drawer__animation-wrapper.ava-drawer--animated {
        animation: slideFromTop 0.25s cubic-bezier(0.16, 1, 0.3, 1);
      }
    }
  }

  /* Bottom Position */
  &--bottom {
    bottom: 0;
    left: 0;
    right: 0;
    border-top: var(--drawer-border);
    border-bottom: none;

    &.ava-drawer--open {
      .ava-drawer__animation-wrapper.ava-drawer--animated {
        animation: slideFromBottom 0.25s cubic-bezier(0.16, 1, 0.3, 1);
      }
    }
  }

  /* ===================================================================
     SIZE VARIANTS
     =================================================================== */
  
  /* Small Size */
  &--small {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 320px;
      max-width: 90vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 200px;
      max-height: 50vh;
    }
  }

  /* Medium Size (Default) */
  &--medium {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 480px;
      max-width: 90vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 300px;
      max-height: 60vh;
    }
  }

  /* Large Size */
  &--large {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 640px;
      max-width: 90vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 400px;
      max-height: 70vh;
    }
  }

  /* Extra Large Size */
  &--extra-large {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 800px;
      max-width: 95vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 500px;
      max-height: 80vh;
    }
  }

  /* Full Size */
  &--full {
    &.ava-drawer--left,
    &.ava-drawer--right {
      width: 100vw;
    }

    &.ava-drawer--top,
    &.ava-drawer--bottom {
      height: 100vh;
    }
  }





  /* No Animation */
  &--none {
    transition: none !important;

    .ava-drawer__animation-wrapper {
      animation: none !important;
    }
  }
}

/* ===================================================================
   DRAWER CONTENT STRUCTURE
   =================================================================== */
.ava-drawer__content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* ===================================================================
   HEADER STYLES
   =================================================================== */
.ava-drawer__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--drawer-header-padding);
  border-bottom: var(--drawer-header-border);
  background: var(--drawer-header-background);
  flex-shrink: 0;
}

.ava-drawer__header-content {
  flex: 1;
  min-width: 0; /* Allows text truncation */
}

.ava-drawer__title-section {
  margin-bottom: var(--global-spacing-2);

  &:last-child {
    margin-bottom: 0;
  }
}

.ava-drawer__title {
  margin: 0;
  font: var(--drawer-title-font);
  color: var(--drawer-title-color);
  font-weight: var(--drawer-title-weight);
  line-height: var(--drawer-title-line-height);
}

.ava-drawer__subtitle {
  margin: var(--drawer-subtitle-margin);
  font: var(--drawer-subtitle-font);
  color: var(--drawer-subtitle-color);
  font-weight: var(--drawer-subtitle-weight);
  line-height: var(--drawer-subtitle-line-height);
}

.ava-drawer__header-slot {
  margin-top: var(--global-spacing-2);
}

.ava-drawer__close-section {
  margin-left: var(--drawer-header-gap);
  flex-shrink: 0;
}



/* ===================================================================
   BODY STYLES
   =================================================================== */
.ava-drawer__body {
  flex: 1;
  overflow-y: auto;
  padding: var(--drawer-body-padding);
  background: var(--drawer-body-background);
  color: var(--drawer-body-text-color);
}

/* ===================================================================
   FOOTER STYLES
   =================================================================== */
.ava-drawer__footer {
  padding: var(--drawer-footer-padding);
  border-top: var(--drawer-footer-border);
  background: var(--drawer-footer-background);
  flex-shrink: 0;
}

/* ===================================================================
   RESIZE HANDLE STYLES
   =================================================================== */
.ava-drawer__resize-handle {
  position: absolute;
  background: transparent;
  z-index: 10;

  &:hover {
    background: var(--color-brand-primary);
    opacity: 0.3;
  }

  /* Right drawer resize handle */
  &--right {
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    cursor: ew-resize;
  }

  /* Left drawer resize handle */
  &--left {
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    cursor: ew-resize;
  }

  /* Top drawer resize handle */
  &--top {
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    cursor: ns-resize;
  }

  /* Bottom drawer resize handle */
  &--bottom {
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    cursor: ns-resize;
  }
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */
@media (max-width: 768px) {
  .ava-drawer {
    &--small,
    &--medium,
    &--large,
    &--extra-large {
      &.ava-drawer--left,
      &.ava-drawer--right {
        width: 100vw;
        max-width: 100vw;
      }

      &.ava-drawer--top,
      &.ava-drawer--bottom {
        height: 100vh;
        max-height: 100vh;
      }
    }
  }

  .ava-drawer__header {
    padding: var(--global-spacing-3);
  }

  .ava-drawer__body {
    padding: var(--global-spacing-3);
  }

  .ava-drawer__footer {
    padding: var(--global-spacing-3);
  }
}

/* ===================================================================
   ACCESSIBILITY & REDUCED MOTION
   =================================================================== */
@media (prefers-reduced-motion: reduce) {
  .ava-drawer,
  .ava-drawer-overlay {
    transition: none !important;
  }
}

/* Focus management */
.ava-drawer {
  &:focus {
    outline: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .ava-drawer {
    border: 2px solid;
  }

  .ava-drawer__header {
    border-bottom: 2px solid;
  }

  .ava-drawer__footer {
    border-top: 2px solid;
  }
}
