import { ChangeDetectionStrategy, Component, Input, ElementRef, Renderer2, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BadgesComponent, BadgeState, BadgeSize } from '../badges/badges.component';

export type AvatarSize = 'small' | 'medium' | 'large';
export type AvatarShape = 'pill' | 'square';
export type AvatarTextType = 'status' | 'profile';

@Component({
  selector: 'ava-avatars',
  imports: [CommonModule, BadgesComponent],
  templateUrl: './avatars.component.html',
  styleUrl: './avatars.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AvatarsComponent implements OnInit, OnDestroy {
  @Input() size: AvatarSize = 'large';
  @Input() shape: AvatarShape = 'pill';
  @Input() imageUrl: string = '';
  @Input() statusText?: string;
  @Input() profileText?: string;
  @Input() badgeState?: BadgeState;
  @Input() badgeSize?: BadgeSize;
  @Input() badgeCount?: number;
  @Input() active: boolean = false;
  @Input() processedanddone: boolean = false;
  
  // Simple gradient inputs - just colors!
  @Input() gradientColors: string[] = ['#E91E63', '#FF9800']; // Default pink to orange
  @Input() animationSpeed: number = 2; // seconds (optional)

  private uniqueId: string;
  private styleElement: HTMLStyleElement | null = null;

  constructor(private el: ElementRef, private renderer: Renderer2) {
    this.uniqueId = `avatar-${Math.random().toString(36).substr(2, 9)}`;
  }

  ngOnInit() {
    if (this.processedanddone && this.hasCustomColors()) {
      this.createDynamicStyles();
    }
  }

  private hasCustomColors(): boolean {
    // Check if user provided custom colors (not default pink-orange)
    const defaultColors = ['#E91E63', '#FF9800'];
    return JSON.stringify(this.gradientColors) !== JSON.stringify(defaultColors);
  }

  ngOnDestroy() {
    if (this.styleElement) {
      this.styleElement.remove();
    }
  }

  private createDynamicStyles() {
    // Remove existing style element if it exists
    if (this.styleElement) {
      this.styleElement.remove();
    }

    this.styleElement = this.renderer.createElement('style');
    this.renderer.appendChild(document.head, this.styleElement);

    const colors = this.gradientColors;
    
    // For circular avatars - evenly distribute colors around the circle
    const pillGradientStops = colors.map((color, index) => {
      const angle = (index * 360) / colors.length;
      return `${color} ${angle}deg`;
    }).join(', ');

    // For square avatars - evenly distribute colors across the gradient
    const squareGradientStops = colors.map((color, index) => {
      const percentage = (index * 100) / (colors.length - 1);
      return `${color} ${percentage}%`;
    }).join(', ');

    const css = `
      .avatar-container .avatar.avatar--processed-done.${this.uniqueId}::before {
        animation-duration: ${this.animationSpeed}s;
      }
      
      .avatar-container .avatar.avatar--processed-done.${this.uniqueId}.avatar--pill::before {
        background: conic-gradient(
          from 0deg,
          ${pillGradientStops}
        );
      }
      
      .avatar-container .avatar.avatar--processed-done.${this.uniqueId}.avatar--square::before {
        background: linear-gradient(
          45deg,
          ${squareGradientStops}
        );
        background-size: 400% 400%;
      }
    `;

    if (this.styleElement) {
      this.styleElement.textContent = css;
    }
  }

  onImageLoad() {
    if (this.processedanddone) {
      console.log('Processed and done state - continuous rotation started');
    }
  }

  get avatarClasses(): string {
    let classes = `avatar avatar--${this.size} avatar--${this.shape}`;
    
    if (this.active) {
      classes += ' avatar--active';
    }
    
    if (this.processedanddone) {
      classes += ` avatar--processed-done ${this.uniqueId}`;
    }
    
    return classes;
  }

  get hasBadge(): boolean {
    return !!(this.badgeState || this.badgeCount);
  }

  get hasStatusText(): boolean {
    return !!this.statusText;
  }

  get hasProfileText(): boolean {
    return !!this.profileText;
  }

  get hasAnyText(): boolean {
    return this.hasStatusText || this.hasProfileText;
  }
}