$avatar-size-sm: var(--avatar-size-sm);
$avatar-size-md: var(--avatar-size-md);
$avatar-size-lg: var(--avatar-size-lg);
$avatar-background: var(--avatar-background);
$avatar-text-color: var(--avatar-text-color);
$avatar-text-font: var(--avatar-text-font);
$avatar-text-size-sm: var(--avatar-text-size-sm);
$avatar-text-size-md: var(--avatar-text-size-md);
$avatar-status-border-color: var(--avatar-status-border-color);
$avatar-border-radius: var(--avatar-border-radius);

.avatar-container {
  position: relative;
  display: inline-block;

  .avatar-wrapper {
    display: inline-flex;
    padding: 8px;
    align-items: center;
    gap: 8px;
  }

  .avatar {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    gap: 10px;
    flex-shrink: 0;
    aspect-ratio: 1/1;
    background-color: $avatar-background;
    
    // Fixed image sizing
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    
    position: relative;

    &--active {
      border: 3px solid rgb(var(--rgb-brand-primary));
    }

    &--processed-done {
      border: 3px solid transparent;
      background-clip: padding-box;
      
      &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        padding: 3px;
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        
        // Default animation properties (will be overridden by dynamic styles)
        animation: continuousRotatingGradient 2s linear infinite;
      }
      
      // For pill shape (circular) - use conic gradient with default colors
      &.avatar--pill::before {
        border-radius: 50%;
        background: conic-gradient(
          from 0deg,
          #E91E63 0deg,
          #FF9800 180deg,
          #E91E63 360deg
        );
        animation: continuousRotatingGradient 2s linear infinite;
      }
      
      // For square shapes - use proper border animation with default colors
      &.avatar--square::before {
        background: linear-gradient(
          45deg,
          #E91E63 0%,
          #FF9800 25%,
          #E91E63 50%,
          #FF9800 75%,
          #E91E63 100%
        );
        background-size: 400% 400%;
        animation: squareBorderFlow 2s linear infinite;
      }
      
      // Square border radius for different sizes
      &.avatar--large.avatar--square::before {
        border-radius: 12px;
      }
      
      &.avatar--medium.avatar--square::before {
        border-radius: 8px;
      }
      
      &.avatar--small.avatar--square::before {
        border-radius: 4px;
      }
    }

    // Large size variations
    &--large {
      width: $avatar-size-lg;
      height: $avatar-size-lg;

      &.avatar--pill {
        border-radius: $avatar-border-radius;
      }

      &.avatar--square {
        border-radius: 12px;
      }
    }

    // Medium size variations
    &--medium {
      width: $avatar-size-md;
      height: $avatar-size-md;

      &.avatar--pill {
        border-radius: $avatar-border-radius;
      }

      &.avatar--square {
        border-radius: 8px;
      }
    }

    // Small size variations
    &--small {
      width: $avatar-size-sm;
      height: $avatar-size-sm;

      &.avatar--pill {
        border-radius: $avatar-border-radius;
      }

      &.avatar--square {
        border-radius: 4px;
      }
    }
  }
}

// Badge positioning
.avatar-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 1;
}

.avatar-text-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 8px;
}

.avatar-text {
  color: $avatar-text-color;
  text-align: center;
  font-weight: $avatar-text-font;

  &--status {
    font-size: $avatar-text-size-sm;
  }

  &--profile {
    font-size: $avatar-text-size-md;
  }
}

// Animation keyframes remain the same
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Continuous rotating gradient animation for circular avatars
@keyframes continuousRotatingGradient {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Animation for square avatars - flows gradient around the border
@keyframes squareBorderFlow {
  0% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}