import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AvatarsComponent } from './avatars.component';
import { BadgesComponent } from '../badges/badges.component';
import { By } from '@angular/platform-browser';

describe('AvatarsComponent', () => {
  let component: AvatarsComponent;
  let fixture: ComponentFixture<AvatarsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AvatarsComponent, BadgesComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(AvatarsComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should return correct avatar classes', () => {
    component.size = 'small';
    component.shape = 'square';
    expect(component.avatarClasses).toBe('avatar avatar--small avatar--square');
  });

  it('should return hasBadge as true when badgeCount is set', () => {
    component.badgeState = undefined;
    component.badgeCount = 3;
    expect(component.hasBadge).toBeTrue();
  });

  it('should return hasStatusText as true when statusText is set', () => {
    component.statusText = 'Online';
    expect(component.hasStatusText).toBeTrue();
  });

  it('should return hasProfileText as true when profileText is set', () => {
    component.profileText = 'John Doe';
    expect(component.hasProfileText).toBeTrue();
  });

  it('should return hasAnyText as true when either status or profile text is set', () => {
    component.statusText = 'Active';
    expect(component.hasAnyText).toBeTrue();

    component.statusText = undefined;
    component.profileText = 'Developer';
    expect(component.hasAnyText).toBeTrue();
  });

  it('should not show badge if hasBadge is false', () => {
    component.badgeState = undefined;
    component.badgeCount = undefined;
    fixture.detectChanges();

    const badge = fixture.debugElement.query(By.css('ava-badges'));
    expect(badge).toBeNull();
  });

  it('should show status text when hasStatusText is true', () => {
    component.statusText = 'Available';
    fixture.detectChanges();

    const statusEl = fixture.debugElement.query(By.css('.avatar-text--status'));
    expect(statusEl.nativeElement.textContent).toContain('Available');
  });

  it('should show profile text when hasProfileText is true', () => {
    component.profileText = 'Jane';
    fixture.detectChanges();

    const profileEl = fixture.debugElement.query(By.css('.avatar-text--profile'));
    expect(profileEl.nativeElement.textContent).toContain('Jane');
  });

  it('should show both status and profile text when hasAnyText is true', () => {
    component.statusText = 'Active';
    component.profileText = 'Admin';
    fixture.detectChanges();

    const statusEl = fixture.debugElement.query(By.css('.avatar-text--status'));
    const profileEl = fixture.debugElement.query(By.css('.avatar-text--profile'));
    expect(statusEl).toBeTruthy();
    expect(profileEl).toBeTruthy();
  });

  it('should bind background image when imageUrl is set', () => {
    const imageUrl = 'https://example.com/avatar.jpg';
    component.imageUrl = imageUrl;
    fixture.detectChanges();

    const avatarDiv = fixture.debugElement.query(By.css('.avatar'));
    expect(avatarDiv.styles['background-image']).toContain(imageUrl);
  });

  it('should set background to none when imageUrl is empty', () => {
    component.imageUrl = '';
    fixture.detectChanges();

    const avatarDiv = fixture.debugElement.query(By.css('.avatar'));
    expect(avatarDiv.styles['background-image']).toBe('none');
  });
});
