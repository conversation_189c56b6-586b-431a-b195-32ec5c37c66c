.ava-advanced-card-container {
    .ava-advanced-card {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 2.5rem;
        color: #fff;
        overflow: hidden;
        transition: all 0.3s ease;
        text-align: center;

        .card-content {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            p {
                color: rgba(255, 255, 255, 0.7);
            }
        }

        &:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }


    }
}