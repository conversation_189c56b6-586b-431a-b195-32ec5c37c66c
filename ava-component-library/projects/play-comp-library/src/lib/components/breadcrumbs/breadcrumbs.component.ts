import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { IconComponent } from '../icon/icon.component';

interface BreadcrumbItem {
  label?: string;           // Optional text
  icon?: string;           // Optional icon name
  url: string;             // Required URL
  active: boolean;         // Required active state
}

@Component({
  selector: 'ava-breadcrumbs',
  imports: [CommonModule, RouterModule, IconComponent],
  templateUrl: './breadcrumbs.component.html',
  styleUrl: './breadcrumbs.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})

export class BreadcrumbsComponent {
  @Input() breadcrumbs: BreadcrumbItem[] = [];
  @Input() sizeClass: 'small' | 'medium' | 'large' = 'medium';
  @Input() separatorIcon: string = 'chevron-right'; // Separator icon
  @Input() separatorSize: number = 14; // Separator icon size
  @Input() collapsible: boolean = true; // Enable collapsible behavior
  @Input() maxVisibleItems: number = 5; // Maximum items before collapsing

  clickedBreadcrumbIndex: number | null = null;

  constructor(private router: Router) {}

  onBreadcrumbClick(event: Event, index: number): void {
    // Always prevent default and handle navigation ourselves
    event.preventDefault();
    
    if (!this.shouldCollapse) {
      // For short breadcrumbs, set clickedBreadcrumbIndex to control display
      this.clickedBreadcrumbIndex = index;
      const clickedBreadcrumb = this.breadcrumbs[index];
      if (clickedBreadcrumb && clickedBreadcrumb.url) {
        this.router.navigate([clickedBreadcrumb.url]);
      }
      return;
    }

    // For long breadcrumbs, use the existing logic
    this.clickedBreadcrumbIndex = index;
    const clickedBreadcrumb = this.breadcrumbs[index];
    if (clickedBreadcrumb && clickedBreadcrumb.url) {
      this.router.navigate([clickedBreadcrumb.url]);
    }
  }

  onEllipsisClick(): void {
    // When clicking on "...", expand to show more breadcrumbs
    // Show up to the second-to-last item (keeping the last one separate)
    this.clickedBreadcrumbIndex = this.breadcrumbs.length - 2;
  }

  getOriginalIndex(displayedIndex: number): number {
    // For non-collapsible breadcrumbs, return the index as-is
    if (!this.shouldCollapse) {
      return displayedIndex;
    }

    // Map displayed breadcrumb index to original breadcrumb index
    const displayed = this.displayedBreadcrumbs;
    const original = this.breadcrumbs;


    // Find the original index by matching the breadcrumb object
    const displayedBreadcrumb = displayed[displayedIndex];
    const originalIndex = original.findIndex(b => b.url === displayedBreadcrumb.url);


    return originalIndex;
  }

  // Get the appropriate icon color based on breadcrumb state
  getIconColor(isLast: boolean): string {
    if (isLast) {
      return 'var(--breadcrumbs-item-current-text)'; // Current/active item color
    }
    return 'var(--breadcrumbs-item-text)'; // Regular item color
  }

  onBreadcrumbKeydown(event: KeyboardEvent, index: number): void {
    if (event.key === 'ArrowRight') {
      if (index < this.breadcrumbs.length - 1) {
        this.clickedBreadcrumbIndex = index + 1;
      }
    } else if (event.key === 'ArrowLeft') {
      if (index > 0) {
        this.clickedBreadcrumbIndex = index - 1;
      }
    } else if (event.key === 'Enter' || event.key === ' ') {
      this.onBreadcrumbClick(event, index);
    }
  }

  get displayedBreadcrumbs() {
    // If a breadcrumb was clicked, show path up to that point
    if (this.clickedBreadcrumbIndex !== null) {
      const pathToClicked = this.breadcrumbs.slice(0, this.clickedBreadcrumbIndex + 1);
      
      // For short breadcrumbs or when path is short, return the path as-is
      if (!this.shouldCollapse || pathToClicked.length <= this.maxVisibleItems) {
        return pathToClicked;
      }

      // For long paths, show first + last two of the path
      return [
        pathToClicked[0], // First item
        ...pathToClicked.slice(-2) // Last two of the path to clicked
      ];
    }

    // If not collapsible or total breadcrumbs <= maxVisibleItems, show all
    if (!this.collapsible || this.breadcrumbs.length <= this.maxVisibleItems) {
      return this.breadcrumbs;
    }

    // Default collapsed view: first + last two
    return this.getCollapsedBreadcrumbs();
  }

  // Check if breadcrumbs should be collapsed
  get shouldCollapse(): boolean {
    return this.collapsible && this.breadcrumbs.length > this.maxVisibleItems;
  }

  // Check if we should show ellipsis
  get shouldShowEllipsis(): boolean {
    if (!this.shouldCollapse) return false;

    const displayed = this.displayedBreadcrumbs;

    // Show ellipsis if we have exactly 3 displayed items and there are more items in between
    if (displayed.length === 3) {
      // Check if it's first + last two pattern (meaning there are hidden items)
      const firstItem = displayed[0];
      const secondItem = displayed[1];

      // Find indices in original breadcrumbs
      const firstIndex = this.breadcrumbs.findIndex(b => b.url === firstItem.url);
      const secondIndex = this.breadcrumbs.findIndex(b => b.url === secondItem.url);

      // Show ellipsis if there's a gap between first and second item
      return firstIndex === 0 && secondIndex > firstIndex + 1;
    }

    return false;
  }

  private getCollapsedBreadcrumbs(): BreadcrumbItem[] {
    if (this.breadcrumbs.length <= 3) {
      return this.breadcrumbs;
    }

    // Return first + last two
    return [
      this.breadcrumbs[0],
      ...this.breadcrumbs.slice(-2)
    ];
  }


}