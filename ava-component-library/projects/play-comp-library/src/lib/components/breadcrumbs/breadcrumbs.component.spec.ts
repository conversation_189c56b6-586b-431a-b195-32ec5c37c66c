import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter } from '@angular/router';
import { BreadcrumbsComponent } from './breadcrumbs.component';

describe('BreadcrumbsComponent', () => {
  let component: BreadcrumbsComponent;
  let fixture: ComponentFixture<BreadcrumbsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BreadcrumbsComponent],
      providers: [provideRouter([])]
    })
    .compileComponents();

    fixture = TestBed.createComponent(BreadcrumbsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display breadcrumbs when provided', () => {
    component.breadcrumbs = [
      { label: 'Home', url: '/home', active: false },
      { label: 'Category', url: '/category', active: false },
      { label: 'Product', url: '/product', active: true }
    ];
    fixture.detectChanges();

    const breadcrumbItems = fixture.nativeElement.querySelectorAll('.breadcrumb li');
    expect(breadcrumbItems.length).toBe(3);
  });

  it('should handle keyboard navigation', () => {
    component.breadcrumbs = [
      { label: 'Home', url: '/home', active: false },
      { label: 'Category', url: '/category', active: false },
      { label: 'Product', url: '/product', active: true }
    ];

    const event = new KeyboardEvent('keydown', { key: 'ArrowRight' });
    component.onBreadcrumbKeydown(event, 0);
    expect(component.clickedBreadcrumbIndex).toBe(1);
  });

  it('should apply size classes correctly', () => {
    component.sizeClass = 'large';
    fixture.detectChanges();

    const breadcrumbElement = fixture.nativeElement.querySelector('.breadcrumb');
    expect(breadcrumbElement.classList).toContain('large');
  });

  it('should handle displayedBreadcrumbs correctly', () => {
    component.breadcrumbs = [
      { label: 'Home', url: '/home', active: false },
      { label: 'Category', url: '/category', active: false },
      { label: 'Product', url: '/product', active: true }
    ];
    component.clickedBreadcrumbIndex = 1;

    const displayed = component.displayedBreadcrumbs;
    expect(displayed.length).toBe(2);
    expect(displayed[0].label).toBe('Home');
    expect(displayed[1].label).toBe('Category');
  });
});
