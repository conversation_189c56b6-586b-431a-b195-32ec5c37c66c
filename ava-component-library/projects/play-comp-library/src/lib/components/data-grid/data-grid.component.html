<div class="ava-data-table-wrapper">
    <div class="data-table-wrapper">
        <table class="ava-data-table" [ngClass]="zerbaLine ? 'zebraline': ''">
            <thead>
                <tr>
                    <ng-container *ngFor="let column of columns; let i = index">
                        <th (click)="onSort(column)" [style.cursor]="column.sortable ? 'pointer' : 'default'">
                            <div class="cell-wrapper">
                                <div class="grid-column-container">
                                    <ng-container *ngIf="column.headerCellDef">
                                        <ng-container
                                            *ngTemplateOutlet="column.headerCellDef.templateRef"></ng-container>
                                    </ng-container>
                                    <ng-container>
                                        <span class="sort-icon" *ngIf="sortColumn === column.name">
                                            <ng-container [ngSwitch]="sortDirection">
                                                <ava-icon *ngSwitchCase="'asc'" iconName="move-up" iconSize="15"
                                                    alt="Ascending" />
                                                <ava-icon *ngSwitchCase="'desc'" iconName="move-down" iconSize="15"
                                                    alt="Descending" />
                                                <!-- Optional: no icon if no direction -->
                                            </ng-container>
                                        </span>
                                        <span class="sort-icon" *ngIf="!sortDirection && column.sortable">
                                            <ava-icon iconName="move-up" iconSize="15" alt="Ascending" />
                                        </span>
                                    </ng-container>


                                </div>
                                <span class="filter" *ngIf="column.filter">
                                    <ava-icon *ngIf="isFilterActive(column.name)" iconName="list-filter" iconSize="15"
                                        alt="filter" [cursor]="true" iconColor="var(--grid-filter-active-color)"
                                        (userClick)="openPanel(column.name,$event)" />
                                    <ava-icon *ngIf="!isFilterActive(column.name)" iconName="list-filter" iconSize="15"
                                        alt="filter" [cursor]="true" (userClick)="openPanel(column.name,$event)" />
                                </span>
                                <div [ngClass]="{ 'last': column.filter && i === columns.length - 2 }"
                                    class="filter-wrapper" #filterWrapper
                                    *ngIf="isFilterOpen && checkForOpen(column.name) && column.filter"
                                    (click)="stopPropagation($event)">
                                    <div class="link-wrapper">
                                        <ava-link href="Javascript:void()" size="small" label="Clear All"
                                            color="primary" (userClick)="clearAll($event)"></ava-link>
                                    </div>
                                    <ava-select height="200" size="sm" aria-label="Filter condition" role="listbox"
                                        (selectionChange)=selectFilter($event)>
                                        <ava-select-option *ngFor="let opt of defaultFilterConditions" role="option"
                                            [attr.aria-selected]="selectedFilter === opt.value"
                                            [selected]="opt.value === selectedFilter" [value]="opt.value">
                                            {{ opt.label }}
                                        </ava-select-option>
                                    </ava-select>
                                    <ava-textbox [mapper]="column.name" size="sm" #textboxRef
                                        [attr.data-column]="column.name" placeholder="Enter value"></ava-textbox>
                                    <div class="default-filter-actions">

                                        <ava-button label="Clear" variant="secondary" size="small"
                                            (userClick)="clearFilter(column.name, $event)"
                                            pressedEffect="ripple"></ava-button>
                                        <ava-button label="Filter" variant="primary" size="small"
                                            (userClick)="applyFilter(column.name,$event)"
                                            pressedEffect="ripple"></ava-button>
                                    </div>
                                </div>
                            </div>

                        </th>
                    </ng-container>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let row of sortedData; let i = index">
                    <ng-container *ngFor=" let column of columns">
                        <td>
                            <ng-container *ngIf="column.cellDef" [ngTemplateOutlet]="column.cellDef.templateRef"
                                [ngTemplateOutletContext]="{ $implicit: row ,index: i}"></ng-container>
                        </td>
                    </ng-container>
                </tr>
            </tbody>
        </table>
    </div>
</div>