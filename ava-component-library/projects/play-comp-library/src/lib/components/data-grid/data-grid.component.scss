.ava-data-table-wrapper {
    table-layout: auto;

    .data-table-wrapper {
        position: relative;
        overflow-x: auto;
        width: 100%;
        min-height: 200px;
        border: 1px solid var(--table-border);
        border-radius: 10px;

        .ava-data-table {
            table-layout: auto;
            width: max-content;
            min-width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-family: var(--grid-font-family-body);
            color: var(--grid-text-color);

            .cell-wrapper {
                position: relative;

                .filter {
                    position: absolute;
                    top: 0;
                    right: 0;
                }
            }

            .filter-wrapper {
                position: absolute;
                z-index: 999;
                background-color: var(--grid-background-color-odd);
                border: 1px solid var(--grid-border);
                border-radius: 8px;
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
                padding: 1rem;
                min-width: 240px;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                right: -227px;

                &.last {
                    right: 0;
                }

                ava-button {
                    &:first-child {
                        margin-right: 10px;
                    }
                }

                .link-wrapper {
                    display: flex;
                    justify-content: flex-end;
                }

                .default-filter-actions {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
            }

            th,
            td {
                padding: var(--grid-cell-paading);
                text-align: left;


            }

            td {
                ava-icon {
                    margin-right: 1rem;
                }

                border-bottom: 1px solid #c3c3c3;


            }

            thead {
                background: var(--grid-background-color-even);
            }

            tbody tr {
                transition: background-color 0.3s ease;

                &:last-child {
                    td {
                        border-bottom: none;
                    }
                }

                .cell-link {
                    color: inherit;
                    text-decoration: none;
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }

            &.zebraline {
                tbody tr {
                    &:nth-child(odd) {
                        background-color: var(--grid-background-color-odd);
                    }

                    &:nth-child(even) {
                        background: var(--grid-background-color-even);
                    }
                }
            }




        }


    }
}