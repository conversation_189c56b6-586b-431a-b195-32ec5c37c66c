import { ComponentFixture, TestBed } from '@angular/core/testing';
import { GlassButtonComponent } from './glass-button.component';

describe('GlassButtonComponent', () => {
  let component: GlassButtonComponent;
  let fixture: ComponentFixture<GlassButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GlassButtonComponent],
    }).compileComponents();
    fixture = TestBed.createComponent(GlassButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render label', () => {
    component.label = 'Glass Test';
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).toContain('Glass Test');
  });

  it('should apply correct glass variant class', () => {
    component.glassVariant = 'glass-75';
    fixture.detectChanges();
    const button = fixture.nativeElement.querySelector('button');
    expect(button.className).toContain('ava-button--glass-75');
  });

  it('should emit userClick on click', () => {
    spyOn(component.userClick, 'emit');
    const button = fixture.nativeElement.querySelector('button');
    button.click();
    expect(component.userClick.emit).toHaveBeenCalled();
  });
});
