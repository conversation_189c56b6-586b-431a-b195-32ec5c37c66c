import {
  ChangeDetectionStrategy,
  Component,
  Input,
  Output,
  EventEmitter,
  TemplateRef,
  ViewChild,
  ChangeDetectorRef,
  CUSTOM_ELEMENTS_SCHEMA,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvaTextboxComponent } from '../textbox/ava-textbox.component';
import { AvaTagComponent } from '../tags/tags.component';
import { Observable, isObservable, of, Subject } from 'rxjs';
import { debounceTime, switchMap, startWith, map } from 'rxjs/operators';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IconComponent } from '../icon/icon.component';

export interface AvaAutocompleteOption {
  label: string;
  value: string;
  icon?: string;
  group?: string;
  [key: string]: string | boolean | number | undefined;
}

@Component({
  selector: 'ava-autocomplete',
  standalone: true,
  imports: [CommonModule, AvaTextboxComponent, AvaTagComponent, IconComponent],
  templateUrl: './ava-autocomplete.component.html',
  styleUrl: './ava-autocomplete.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: AvaAutocompleteComponent,
      multi: true,
    },
  ],
})
export class AvaAutocompleteComponent implements ControlValueAccessor {
  @Input() options:
    | AvaAutocompleteOption[]
    | Observable<AvaAutocompleteOption[]> = [];
  @Input() placeholder = '';
  @Input() label = '';
  @Input() error = '';
  @Input() helper = '';
  @Input() loading = false;
  @Input() disabled = false;
  @Input() clearable = true;
  @Input() minLength = 1;
  @Input() maxOptions = 10;
  @Input() noResultsText = 'No results found';
  @Input() debounce = 200;
  @Input() optionTemplate?: TemplateRef<unknown>;
  @Input() multi = false;
  @Input() fullWidth = false;
  @Input() required = false;
  @Input() readonly = false;
  @Input() name = '';
  @Input() id = '';
  @Input() ariaLabel = '';
  @Input() ariaLabelledby = '';
  @Input() ariaDescribedby = '';
  @Input() showDefaultOptions = false;

  /** Props forwarded to ava-tag for multi-select customization */
  @Input() tagColor?:
    | 'default'
    | 'primary'
    | 'success'
    | 'warning'
    | 'error'
    | 'info'
    | 'custom';
  @Input() tagVariant?: 'filled' | 'outlined';
  @Input() tagSize?: 'sm' | 'md' | 'lg';
  @Input() tagPill?: boolean;
  @Input() tagRemovable?: boolean;
  @Input() tagDisabled?: boolean;
  @Input() tagIcon?: string;
  @Input() tagIconPosition?: 'start' | 'end';
  @Input() tagAvatar?: string;
  @Input() tagCustomStyle?: Record<string, string>;
  @Input() tagCustomClass?: string;
  @Input() tagIconColor?: string;
  

  /** Start icon for the input field */
  @Input() startIcon = '';
  @Input() startIconColor = '';
  @Input() startIconSize = '16px';

  @Output() optionSelected = new EventEmitter<AvaAutocompleteOption>();
  @Output() valueChange = new EventEmitter<string | string[]>();
  @Output() cleared = new EventEmitter<void>();

  @ViewChild('textboxRef', { static: true }) textboxRef!: AvaTextboxComponent;

  query = '';
  filteredOptions: AvaAutocompleteOption[] = [];
  showDropdown = false;
  highlightedIndex = 0;
  selectedOptions: AvaAutocompleteOption[] = [];
  loadingState = false;
  private isUpdatingProgrammatically = false;

  private input$ = new Subject<string>();

  // ControlValueAccessor implementation
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private onChange: (value: unknown) => void = () => { };
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private onTouched: () => void = () => { };

  constructor(private cdr: ChangeDetectorRef) { }

  ngOnInit() {
    this.input$
      .pipe(
        startWith(this.query),
        debounceTime(this.debounce),
        switchMap((query) => this.getOptions(query))
      )
      .subscribe((options) => {
        this.filteredOptions = options.slice(0, this.maxOptions);
        this.loadingState = false;
        this.highlightedIndex = 0;
        this.cdr.markForCheck();
      });
  }

  getOptions(query: string): Observable<AvaAutocompleteOption[]> {
    if (this.loading) {
      this.loadingState = true;
    }
    if (isObservable(this.options)) {
      return (this.options as Observable<AvaAutocompleteOption[]>).pipe(
        map((opts) => this.filterOptions(opts, query))
      );
    } else {
      return of(
        this.filterOptions(this.options as AvaAutocompleteOption[], query)
      );
    }
  }

  filterOptions(
    options: AvaAutocompleteOption[],
    query: string
  ): AvaAutocompleteOption[] {
    if (!query) return options;
    return options.filter((opt) =>
      opt.label.toLowerCase().includes(query.toLowerCase())
    );
  }

  onInput(event: Event) {
    const value = (event.target as HTMLInputElement).value;

    // Skip processing if we're updating programmatically
    if (this.isUpdatingProgrammatically) {
      return;
    }

    this.query = value;
    this.input$.next(value);
    this.valueChange.emit(value);
    this.onChange(
      this.multi ? this.selectedOptions.map((o) => o.value) : value
    );

    // Show dropdown if we have enough characters and options
    const hasMinLength = value.length >= this.minLength;
    const hasOptions = this.filteredOptions.length > 0;
    this.showDropdown = hasMinLength && hasOptions;

    // Reset highlighted index when typing
    if (hasMinLength) {
      this.highlightedIndex = 0;
    }
  }

  onFocus() {
    if (this.showDefaultOptions) {
      // Show all options if showDefaultOptions is true and query is empty
      this.input$.next('');
      this.showDropdown = true;
    } else if (this.query.length >= this.minLength && this.filteredOptions.length) {
      this.showDropdown = true;
    }
  }

  onBlur() {
    // Use a longer delay to prevent interference with option selection
    setTimeout(() => {
      this.showDropdown = false;
      this.cdr.markForCheck();
    }, 200);
  }

  onOptionClick(option: AvaAutocompleteOption) {
    if (this.multi) {
      if (!this.selectedOptions.find((o) => o.value === option.value)) {
        this.selectedOptions.push(option);
        this.valueChange.emit(this.selectedOptions.map((o) => o.value));
        this.onChange(this.selectedOptions.map((o) => o.value));
      }
      this.query = '';
      this.input$.next('');
    } else {
      // Set flag to prevent onInput interference
      this.isUpdatingProgrammatically = true;

      // For single select, update the query to show the selected option label
      this.query = option.label;

      // Emit the option value (not the label)
      this.valueChange.emit(option.value);
      this.onChange(option.value);

      // Hide dropdown after selection
      this.showDropdown = false;

      // Reset flag after a short delay to allow UI update
      setTimeout(() => {
        this.isUpdatingProgrammatically = false;
      }, 100);
    }

    // Emit the selected option
    this.optionSelected.emit(option);

    // Force change detection to ensure UI updates
    this.cdr.detectChanges();
  }

  onClear() {
    this.query = '';
    this.selectedOptions = [];
    this.input$.next('');
    this.cleared.emit();
    this.valueChange.emit(this.multi ? [] : '');
    this.cdr.markForCheck();
  }

  onKeydown(event: KeyboardEvent) {
    if (!this.showDropdown) return;
    if (event.key === 'ArrowDown') {
      this.highlightedIndex =
        (this.highlightedIndex + 1) % this.filteredOptions.length;
      event.preventDefault();
    } else if (event.key === 'ArrowUp') {
      this.highlightedIndex =
        (this.highlightedIndex - 1 + this.filteredOptions.length) %
        this.filteredOptions.length;
      event.preventDefault();
    } else if (event.key === 'Enter') {
      if (this.filteredOptions[this.highlightedIndex]) {
        this.onOptionClick(this.filteredOptions[this.highlightedIndex]);
      }
      event.preventDefault();
    } else if (event.key === 'Escape') {
      this.showDropdown = false;
      event.preventDefault();
    }
  }

  trackByValue(_index: number, option: AvaAutocompleteOption) {
    return option.value;
  }

  removeSelectedOption(opt: AvaAutocompleteOption) {
    this.selectedOptions = this.selectedOptions.filter(
      (o) => o.value !== opt.value
    );
    this.valueChange.emit(this.selectedOptions.map((o) => o.value));
    this.onChange(this.selectedOptions.map((o) => o.value));
    this.cdr.markForCheck();
  }

  // ControlValueAccessor implementation
  writeValue(value: unknown): void {
    if (this.multi) {
      this.selectedOptions = Array.isArray(value)
        ? this.optionsArrayToSelected(value as unknown[])
        : [];
      this.query = '';
    } else {
      // For single select, find the option label that matches the value
      if (typeof value === 'string' && value) {
        const options = Array.isArray(this.options) ? this.options : [];
        const selectedOption = options.find((opt) => opt.value === value);
        this.query = selectedOption ? selectedOption.label : value;
      } else {
        this.query = '';
      }
    }
    this.cdr.markForCheck();
  }

  registerOnChange(fn: (value: unknown) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    this.cdr.markForCheck();
  }

  // Helper for multi-select
  private optionsArrayToSelected(values: unknown[]): AvaAutocompleteOption[] {
    const opts = Array.isArray(this.options) ? this.options : [];
    return opts.filter((opt) => values.includes(opt.value));
  }
}
