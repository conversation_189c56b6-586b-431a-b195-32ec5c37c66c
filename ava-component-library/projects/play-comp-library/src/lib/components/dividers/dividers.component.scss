
.ava-divider {
  padding: 10px;
  gap: 10px;
  opacity: 1;
  


/* Horizontal Divider */
&.horizontal {
  width: 100%;
  height: 10px;
}
&.vertical{
  width: 1px;
  height: 100%;
  min-height: 400px; 
}

/* Solid Divider */
&.horizontal.solid {
  border-top: 1px solid var(--divider-color);
}

&.vertical.solid {
  border-left: 1px solid var(--divider-color);
}

/* Dashed Divider */
&.horizontal.dashed {
  border-top: 1px dashed var(--divider-color);
}

&.vertical.dashed {
  border-left: 1px dashed var(--divider-color);
}

/* Dotted Divider */
&.horizontal.dotted {
  border-top: 1px dotted var(--divider-color);
}

&.vertical.dotted {
  border-left: 1px dotted var(--divider-color);
}

/* Gradient Divider */
&.gradient.horizontal {
  background: var(--divider-background-gradient);
  height: 1px; 
  padding: 0;
  margin: 0;
  border: none;
}

&.gradient.vertical {
 background:var(--divider-background-gradient);
  padding: 0;
  border: none;
}

}
