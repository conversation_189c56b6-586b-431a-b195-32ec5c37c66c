<!-- Preview Layout -->
<div *ngIf="preview" class="custom-file-upload">
  <div class="left-box">
    <div class="upload-container new" role="dialog" [ngClass]="[theme, enableAnimation ? 'animated' : '']"
      [attr.aria-labelledby]="'upload-title-' + uniqueId" [attr.aria-describedby]="'upload-desc-' + uniqueId"
      (dragover)="onDragOver($event)" (drop)="onDrop($event)">
      <div class="file-upload-header">
        <div class="file-upload-header-title" [id]="'upload-title-' + uniqueId">
          <!-- Title is hidden for new variant -->
        </div>
      </div>

      <label class="upload-area" [attr.for]="'fileInput-' + uniqueId" [attr.aria-label]="'Click here to upload a file'">
        <input [id]="'fileInput-' + uniqueId" type="file" (change)="onFileSelected($event)"
          [attr.accept]="allowAccepted()" [attr.aria-describedby]="'file-input-desc-' + uniqueId" hidden #fileInput />

        <div class="upload-placeholder">
          <!-- Supported file formats for new variant -->
          <div class="new-variant-supported-formats">
            <p class="supported-file" [id]="'upload-desc-' + uniqueId">
              Supported file formats
            </p>
            <p class="file-formats">{{ allowedFormatsList.join(", ") | uppercase }}</p>
          </div>

          <div *ngIf="uploadedFiles.length > 0; else dragToUpload">
            <p class="click-here" [id]="'file-input-desc-' + uniqueId">
              Your file was added successfully
            </p>
            <p class="click-here active" [id]="'file-input-desc-' + uniqueId">
              {{
              singleFileMode && uploadedFiles.length > 0
              ? "Click to replace file"
              : "Click here to add more"
              }}
            </p>
          </div>

          <ng-template #dragToUpload>
            <p class="click-here" [id]="'file-input-desc-' + uniqueId">
              Drag and drop your file here <br> or
            </p>
          </ng-template>

          <!-- Upload Button for New Variant -->
          <div class="upload-button-container">
            <ava-button variant="primary" [label]="'Upload File'" size="medium" height="48px" width="139px"
              (click)="fileInput.click()"></ava-button>
          </div>
        </div>
      </label>

      <!-- Error Messages -->
      @if (fileFormatError) {
      <div class="error-message" role="alert">
        Invalid file type. Allowed formats:{{
        allowedFormatsList.join(", ") | uppercase
        }}.
      </div>
      } @if (fileSizeError) {
      <div class="error-message" role="alert">
        File is too large. Maximum size allowed is {{ sizeFormat(maxFileSize) }}
      </div>
      } @if (maxFilesError) {
      <div class="error-message" role="alert">
        Maximum of {{ maxFiles }} files allowed
      </div>
      }
    </div>
  </div>

  <div class="right-box" *ngIf="uploadedFiles && uploadedFiles.length > 0">
    <div class=" file-review-container">
      <h3>File Preview</h3>
      <div *ngFor="let file of uploadedFiles" class="file-review-item">
        <div class="file-icon" [ngClass]="'file-icon-' + getFileExtension(file.name)">
          {{ getFileExtension(file.name) }}
        </div>

        <div class="file-info">
          <p>{{ file.name }}</p>
        </div>

        <div class="file-actions">
          <ava-icon iconName="trash" [iconColor]="'red'" [iconSize]="20" class="delete-icon"
            (click)="removeNewFile(file)" style="cursor:pointer;" aria-label="Delete file"></ava-icon>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Default Layout (existing code) -->
<div *ngIf="!preview" class="upload-container" role="dialog" [ngClass]="[theme, enableAnimation ? 'animated' : '']"
  [attr.aria-labelledby]="'upload-title-' + uniqueId" [attr.aria-describedby]="'upload-desc-' + uniqueId"
  (dragover)="onDragOver($event)" (drop)="onDrop($event)">
  <div class="file-upload-header">
    <div class="file-upload-header-title" [id]="'upload-title-' + uniqueId">
      {{ componentTitle }}
    </div>
    <button *ngIf="showDialogCloseIcon" class="close-button" (click)="closeUpload()"
      [attr.aria-label]="'Close upload dialog'">
      <ava-icon iconName="X" [iconSize]="24" iconColor="#a1a1a1"></ava-icon>
    </button>
  </div>

  <p class="supported-file" [id]="'upload-desc-' + uniqueId">
    {{ supportedFormatLabel }}
  </p>
  <p class="file-formats">{{ allowedFormatsList.join(", ") | uppercase }}</p>

  <label class="upload-area" [attr.for]="'fileInput-' + uniqueId" [attr.aria-label]="'Click here to upload a file'">
    <input [id]="'fileInput-' + uniqueId" type="file" (change)="onFileSelected($event)" [attr.accept]="allowAccepted()"
      [attr.aria-describedby]="'file-input-desc-' + uniqueId" hidden />
    <div class="upload-placeholder">
      <ava-icon iconName="image" aria-hidden="true" [iconSize]="24" [iconColor]="'#a1a1a1'"></ava-icon>
      <div class="" *ngIf="uploadedFiles.length > 0; else dragToUpload">
        <p class="click-here" [id]="'file-input-desc-' + uniqueId">
          Your file was added successfully
        </p>
        <p class="click-here active" [id]="'file-input-desc-' + uniqueId">
          {{
          singleFileMode && uploadedFiles.length > 0
          ? "Click to replace file"
          : "Click here to add more"
          }}
        </p>
      </div>
      <ng-template #dragToUpload>
        <p class="click-here" [id]="'file-input-desc-' + uniqueId">
          Drag and Drop your file here
        </p>

        <p class="click-here" [id]="'file-input-desc-' + uniqueId">
          {{
          singleFileMode && uploadedFiles.length > 0
          ? "Click to replace file"
          : "Click here to upload"
          }}
        </p>
      </ng-template>
    </div>
  </label>

  @if (fileFormatError) {
  <div class="error-message" role="alert">
    Invalid file type. Allowed formats:{{
    allowedFormatsList.join(", ") | uppercase
    }}.
  </div>
  } @if (fileSizeError) {
  <div class="error-message" role="alert">
    File is too large. Maximum size allowed is {{ sizeFormat(maxFileSize) }}
  </div>
  } @if (maxFilesError) {
  <div class="error-message" role="alert">
    Maximum of {{ maxFiles }} files allowed
  </div>
  }

  <div class="file-actions">
    <div class="files-list" *ngIf="uploadedFiles.length > 0 && showSelectedFiles">
      <ng-container *ngIf="!viewAll; else viewAllFiles">
        <div *ngFor="let file of uploadedFiles.slice(0, 2); let i = index">
          <ava-tag [label]="file.name | lowercase" [color]="'success'"
            [iconColor]="theme === 'dark' ? '#a1a1aa' : '#059669'" [variant]="'outlined'" [size]="'sm'"
            [removable]="true" (removed)="removeFile(i)"></ava-tag>
        </div>
        <span *ngIf="uploadedFiles.length > 2">
          ...+{{ uploadedFiles.length - 2 }} more
        </span>
      </ng-container>
      <ng-template #viewAllFiles>
        <div *ngFor="let file of uploadedFiles; let i = index">
          <ava-tag [label]="file.name | lowercase" [color]="'success'"
            [iconColor]="theme === 'dark' ? '#a1a1aa' : '#059669'" [variant]="'outlined'" [size]="'sm'"
            [removable]="true" (removed)="removeFile(i)"></ava-tag>
        </div>
      </ng-template>
      <button type="button" class="viewAll" *ngIf="uploadedFiles.length > 2" (click)="toggleViewAll()"
        [attr.aria-label]="viewAll ? 'View less files' : 'View all files'">
        {{ viewAll ? "View Less" : "View All" }}
      </button>
    </div>
    <ava-button *ngIf="showUploadButton" [label]="'Upload'" variant="primary" size="medium"
      [disabled]="uploadedFiles.length === 0" (click)="uploadFile()">
    </ava-button>
  </div>
</div>