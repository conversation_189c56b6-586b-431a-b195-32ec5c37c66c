import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, output, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

@Component({
  selector: 'ava-link',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './link.component.html',
  styleUrls: ['./link.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class LinkComponent {
  @Input() label = 'Action Link';
  @Input() color: 'success' | 'warning' | 'danger' | 'info' | 'default' | 'primary' | string = 'default';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() underline: boolean = false;
  @Input() href: string = '';
  @Output() userClick = new EventEmitter<Event>();
  constructor(private sanitizer: DomSanitizer) { }
  isHexColor(color: string): boolean {
    return /^#([0-9A-F]{3}){1,2}$/i.test(color);
  }
  anchorClick(event: Event) {
    this.userClick.emit(event);
  }
  get safeHref(): SafeUrl {
    return this.sanitizer.bypassSecurityTrustUrl(this.href || 'javascript:void(0)');
  }
}

