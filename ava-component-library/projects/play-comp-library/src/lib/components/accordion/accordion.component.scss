// Optional: define variables at the top
$accordion-gap: 0.5rem;
$accordion-width: 40rem; // 640px
$accordion-margin-bottom: 1.25rem; // 20px
$accordion-border-radius: 0.5rem; //8px
$accordion-background: var(--glass-background-color);
$accordion-padding: 1.5rem;
$accordion-font-size: var(--accordion-content-font); // 1rem
$accordion-font-md-weight: 500;
$accordion-font-family: var(--accordion-font-family); //inter
$accordion-color-gray: var(--accordion-dark-header-background);
$accordion-dark-bg-color: var(--accordion-dark-content-text);

.accordion-container {
  width: 100%;
  max-width: $accordion-width;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: $accordion-margin-bottom;
  position: relative;
  background: $accordion-background;
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);

  // Gradient border using pseudo-element
  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   border-radius: 16px;
  //   padding: 1px;
  //   background: linear-gradient(-70deg, rgba(255,253,251,1), rgba(241,240,239,1), rgba(228,226,226,1));
  //   mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  //   mask-composite: xor;
  //   -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  //   -webkit-mask-composite: xor;
  //   z-index: -1;
  // }

  .accordion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: $accordion-width; // 640px
    min-height: 4.5rem; // 72px
    padding: $accordion-padding $accordion-margin-bottom; // 24px 20px
    cursor: pointer;
    box-sizing: border-box;
    color: $accordion-color-gray;
    font-family: $accordion-font-family;
    font-size: $accordion-font-size; // 16px
    font-style: normal;
    font-weight: $accordion-font-md-weight;
    line-height: $accordion-font-size; // 16px
    position: relative;
    transition: 
      background-color 0.2s ease,
      transform 0.2s ease;
    
    // Gradient border-bottom that fades at the ends (only when expanded)
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 10%;
      right: 10%;
      height: 1px;
      background: linear-gradient(
        to right,
        transparent 0%,
        rgba(0, 0, 0, 0.3) 30%,
        rgba(0, 0, 0, 0.4) 50%,
        rgba(0, 0, 0, 0.3) 70%,
        transparent 100%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  // Show gradient border only when accordion is expanded
  &.expanded .accordion-header::after {
    opacity: 1;
    
    // Hover effect
    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
      // transform: translateY(-1px);
    }

    // Active/pressed effect
    &:active {
      transform: translateY(0);
      background-color: rgba(0, 0, 0, 0.04);
    }

    // Focus effect for accessibility
    &:focus-visible {
      outline: 2px solid var(--color-border-focus);
      outline-offset: 2px;
    }
  }

  .accordion-body {
    overflow: hidden;
    height: 0;
    transition: height 0.4s ease-in-out;
    // background: $accordion-background;
    // background: rgba(255, 255, 255, 0.4);
    // backdrop-filter: blur(15px) saturate(180%);
    // -webkit-backdrop-filter: blur(15px) saturate(180%);
    // border: 2px solid rgba(255, 255, 255, 0.3);
    // border-radius: 16px;
    // box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    background: none;
  }

  .accordion-content {
    padding: 0rem 1.25rem 1.5rem 1.5rem;
    color: var(--accordion-dark-content-background);
    font-family: $accordion-font-family;
    font-size: $accordion-font-size;
    font-style: normal;
    font-weight: var(--accordion-font-weight);
    line-height: $accordion-padding; // 24px
  }

  .accordion-title-highlight {
    color: $accordion-color-gray;
    font-family: $accordion-font-family;
    font-size: $accordion-font-size;
    font-style: normal;
    font-weight: $accordion-font-md-weight;
    line-height: $accordion-font-size;
  }

  .header-row {
    display: flex;
    align-items: center;
    gap: $accordion-gap;
    width: 100%;

    .accordion-title {
      flex: 1;
      display: flex;
      align-items: center;
    }

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .right-aligned-icon {
      margin-left: auto;
    }
  }

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: $accordion-padding; // 24px
    height: $accordion-padding;
  }


}

  .accordion-icon {
    width: 1.5rem;
    height: 1.5rem;
    // transition: transform 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
    // transform-origin: center;
    transition: transform 0.4s ease-in-out;
  }

  // Rotate icon when accordion is open
  .accordion-container:has(.accordion-body.open) .accordion-icon {
    transform: rotate(180deg);
  }

.accordion-container.accordion-dark {
  background: $accordion-dark-bg-color;
  
  .accordion-title {
    background: $accordion-dark-bg-color;
  }
  
  .accordion-title-highlight {
    background: $accordion-dark-bg-color;
    color: var(--accordion-background);
  }

  .accordion-body {
    background: $accordion-dark-bg-color;
  }
  
  .accordion-content {
    color: var(--accordion-background); //rgb(240, 241, 242);
  }
}
.header-row {
  display: flex;
  align-items: center;
  width: 100%;

  .accordion-title {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right-aligned-icon {
    margin-left: auto;
  }
}
