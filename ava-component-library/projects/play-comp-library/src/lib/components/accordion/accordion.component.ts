import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';

@Component({
  selector: 'ava-accordion',
  imports: [CommonModule,LucideAngularModule],
  standalone: true,
  templateUrl: './accordion.component.html',
  styleUrl: './accordion.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AccordionComponent implements AfterViewInit {
  @Input() expanded = false;
  @Input() animation = true;
  @Input() controlled = false;
  @Input() iconClosed = ''; 
  @Input() iconOpen = '';  
  @Input() titleIcon = '';  
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() type: 'default' | 'titleIcon' = 'default';
  
  contentHeight = 0;
  @ViewChild('bodyRef') bodyRef!: ElementRef;
  get accordionClasses() {
    return {
      animated: this.animation,
      expanded: this.expanded,
    };
  }

  ngAfterViewInit() {
    // Capture content height for smooth animation
    if (this.bodyRef && this.bodyRef.nativeElement) {
      const content = this.bodyRef.nativeElement.querySelector('.accordion-content');
      if (content) {
        this.contentHeight = content.scrollHeight;
      }
    }
  }

  onAccordionKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      this.toggleExpand();
      event.preventDefault();
    }
  }

  toggleExpand() {
    if (!this.controlled) {
      this.expanded = !this.expanded;
    }
  }
}
