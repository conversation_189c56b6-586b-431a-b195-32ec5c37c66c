$checkbox-bg: var(--checkbox-box-background);
$checkbox-border: var(--checkbox-box-checked-border);
$checkbox-border-color: var(--checkbox-box-checked-background);
$checkbox-disable-boder: var(--checkbox-box-border-disabled);
$checkbox-disable-bg: var(--checkbox-box-background-disabled);
$checkbox-disable-icon: var(--checkbox-icon-color-disabled);
$checkbox-disable-cursor: var(--checkbox-cursor-disabled);
$checkbox-checked-bg: var(--checkbox-box-checked-background);
$checkbox-checked-icon-color: var(--checkbox-box-checked-color);
$checkbox-checked-border-color: var(--checkbox-box-checked-background);
$checkbox-label-font: var(--checkbox-label-font);
$checkbox-label-color: var(--checkbox-label-color);
$checkbox-label-color-disabled: var(--checkbox-label-color-disabled);
$checkbox-label-cursor: var(--checkbox-label-cursor);
$checkbox-label-disable-cursor: var(--checkbox-label-cursor-disabled);
$checkbox-border-radius: var(--checkbox-box-border-radius);

.ava-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;

  &.disabled {
    cursor: $checkbox-disable-cursor;
  }

  .checkbox {
    display: flex;
    width: 24px;
    height: 24px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    aspect-ratio: 1/1;
    background-color: $checkbox-bg;
    border: $checkbox-border;
    border-radius: 4px;
    margin-right: 8px;
    transition: border-color 0.3s ease;

    &.checked,
    &.indeterminate {
      border-color: $checkbox-border-color;
    }
  }

  .checkbox-icon {
    width: 20px;
    height: 20px;
    color: $checkbox-border-color;

    .checkmark-path {
      stroke-dasharray: 22;
      stroke-dashoffset: 22;
      stroke-linecap: round;
      stroke-linejoin: round;
      animation: drawCheckmark-default 250ms
        cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;

      &.unchecking {
        stroke-dashoffset: 0;
        animation: eraseCheckmark-default 300ms
          cubic-bezier(0.55, 0.06, 0.68, 0.19) forwards;
      }
    }

    .indeterminate-rect {
      transform-origin: center;
      animation: scaleIn 150ms cubic-bezier(0, 0, 0.65, 1) forwards;
      transform: scale(0);
      border-radius: 0;
    }
  }

  .checkbox-label {
    font-size: $checkbox-label-font;
    color: $checkbox-label-color;
    cursor: $checkbox-label-cursor;
  }

  // Horizontal alignment variant (radio button style)
  &.horizontal {
    display: inline-flex;
    margin-right: 16px;
    margin-bottom: 8px;

    .checkbox {
      margin-right: 6px;
    }

    .checkbox-label {
      white-space: nowrap;
    }
  }

  // Size variants
  &.small {
    .checkbox {
      width: 16px;
      height: 16px;
    }

    .checkbox-icon {
      width: 16px;
      height: 16px;
    }

    .indeterminate-rect {
      width: 12px;
      height: 2px;
    }
  }

  &.medium {
    .checkbox {
      width: 20px;
      height: 20px;
    }

    .checkbox-icon {
      width: 20px;
      height: 20px;
    }

    .indeterminate-rect {
      width: 12px;
      height: 3px;
    }
  }

  &.large {
    .checkbox {
      width: 24px;
      height: 24px;
    }

    .checkbox-icon {
      width: 24px;
      height: 24px;
    }

    .indeterminate-rect {
      width: 12px;
      height: 4px;
    }
  }

  // Disabled state
  &.disabled {
    .checkbox-label {
      color: $checkbox-label-color-disabled;
      cursor: $checkbox-label-disable-cursor;
    }

    .checkbox {
      border-color: $checkbox-disable-boder;
      background-color: $checkbox-disable-bg;

      &.checked,
      &.indeterminate {
        border-color: $checkbox-disable-boder;
        background-color: $checkbox-disable-bg;
      }
    }

    .checkbox-icon {
      color: $checkbox-disable-icon;

      .checkmark-path,
      .indeterminate-rect {
        color: $checkbox-disable-icon;
      }
    }

    &.with-bg .checkbox {
      &.checked,
      &.indeterminate {
        background-color: $checkbox-disable-bg;
        border-color: $checkbox-disable-boder;

        .checkbox-icon {
          color: $checkbox-disable-icon;
        }
      }

      &.unchecking {
        background-color: $checkbox-disable-bg;
        border-color: $checkbox-disable-boder;

        .checkbox-icon {
          color: $checkbox-disable-icon;
        }
      }
    }

    &.animated .checkbox {
      &.checked,
      &.checking,
      &.unchecking {
        border-color: $checkbox-disable-boder;

        &::before {
          background-color: $checkbox-disable-bg;
        }

        .checkbox-icon {
          color: $checkbox-disable-icon;
        }
      }
    }
  }

  // With background variant
  &.with-bg {
    .checkbox {
      transition: none;

      &.checked {
        background-color: $checkbox-checked-bg;
        border-color: $checkbox-checked-border-color;
        animation: fillBg-withbg 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94)
          forwards;

        .checkbox-icon {
          color: $checkbox-checked-icon-color;
        }
      }

      &.unchecking {
        background-color: $checkbox-checked-bg;
        border-color: $checkbox-checked-border-color;
        animation: emptyBg-withbg 150ms cubic-bezier(0.55, 0.06, 0.68, 0.19)
          forwards;

        .checkbox-icon {
          color: $checkbox-checked-icon-color;
        }
      }
    }

    .checkbox-icon {
      .checkmark-path {
        animation: drawCheckmark-withbg 300ms
          cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;

        &.unchecking {
          animation: eraseCheckmark-withbg 150ms
            cubic-bezier(0.55, 0.06, 0.68, 0.19) forwards;
        }
      }
    }

    &.disabled .checkbox {
      &.checked {
        animation: fillBg-disabled 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94)
          forwards;
      }

      &.unchecking {
        animation: emptyBg-disabled 150ms cubic-bezier(0.55, 0.06, 0.68, 0.19)
          forwards;
      }
    }
  }

  // Animated variant - COMPLETE ORIGINAL FUNCTIONALITY
  &.animated {
    .checkbox {
      overflow: hidden;
      position: relative;

      &.checked {
        border-color: $checkbox-checked-border-color;

        &::before {
          content: "";
          position: absolute;
          top: -2px;
          left: -2px;
          width: calc(100% + 4px);
          height: calc(100% + 4px);
          background-color: $checkbox-checked-bg;
          border-radius: 4px;
          transform: scale(0);
          transform-origin: bottom left;
          animation: fillBackground 300ms ease-out forwards;
          z-index: 1;
        }

        .checkbox-icon {
          color: $checkbox-checked-icon-color;
          position: relative;
          z-index: 2;
        }
      }

      &.checking {
        border-color: $checkbox-checked-border-color;

        &::before {
          content: "";
          position: absolute;
          top: -2px;
          left: -6px;
          width: calc(100% + 4px);
          height: calc(100% + 4px);
          background-color: $checkbox-checked-bg;
          border-radius: 10px;
          transform: scale(0);
          transform-origin: bottom left;
          animation: fillBackground 300ms ease-out forwards;
          z-index: 1;
        }

        .checkbox-icon {
          color: $checkbox-checked-icon-color;
          position: relative;
          z-index: 2;
        }
      }

      &.unchecking {
        border-color: $checkbox-checked-border-color;

        &::before {
          content: "";
          position: absolute;
          top: -2px;
          left: -2px;
          width: calc(100% + 4px);
          height: calc(100% + 4px);
          background-color: $checkbox-checked-bg;
          border-radius: 10px;
          transform: scale(1.5);
          transform-origin: bottom left;
          animation: emptyBackground 300ms ease-out forwards;
          z-index: 1;
        }

        .checkbox-icon {
          color: $checkbox-checked-icon-color;
          position: relative;
          z-index: 2;
        }
      }
    }

    .checkbox-icon {
      .checkmark-path {
        animation: drawCheckmark-animated 300ms
          cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        animation-delay: 300ms;

        &.unchecking {
          animation: eraseCheckmark-animated 150ms
            cubic-bezier(0.55, 0.06, 0.68, 0.19) forwards;
          animation-delay: 0s;
        }
      }
    }
  }
}

// Add box-shadow on hover for the entire checkbox row
.ava-checkbox:hover .checkbox {
  box-shadow: 0 0 2px var(--checkbox-box-checked-background);
}

// All original keyframes preserved
@keyframes drawCheckmark-default {
  0% {
    stroke-dashoffset: 22;
  }

  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes eraseCheckmark-default {
  0% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: 22;
  }
}

@keyframes drawCheckmark-withbg {
  0% {
    stroke-dashoffset: 22;
  }

  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes eraseCheckmark-withbg {
  0% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: 22;
  }
}

@keyframes drawCheckmark-animated {
  0% {
    stroke-dashoffset: 22;
  }

  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes eraseCheckmark-animated {
  0% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: 22;
  }
}

@keyframes fillBackground {
  from {
    transform: scale(0);
  }

  to {
    transform: scale(1.5);
  }
}

@keyframes emptyBackground {
  from {
    transform: scale(1.5);
  }

  to {
    transform: scale(0);
  }
}

@keyframes fillBg-withbg {
  0% {
    background-color: transparent;
    border-color: $checkbox-checked-border-color;
  }

  100% {
    background-color: $checkbox-checked-bg;
    border-color: $checkbox-checked-border-color;
  }
}

@keyframes emptyBg-withbg {
  0% {
    background-color: $checkbox-checked-bg;
    border-color: $checkbox-checked-border-color;
  }

  100% {
    background-color: transparent;
    border-color: $checkbox-checked-border-color;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }

  to {
    transform: scale(1);
  }
}
