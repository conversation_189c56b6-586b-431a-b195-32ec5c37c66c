import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { CheckboxComponent } from './checkbox.component';
import { By } from '@angular/platform-browser';

describe('CheckboxComponent', () => {
  let component: CheckboxComponent;
  let fixture: ComponentFixture<CheckboxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CheckboxComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(CheckboxComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should emit isCheckedChange on default toggle (checked -> unchecked)', fakeAsync(() => {
    component.variant = 'default';
    component.isChecked = true;
    spyOn(component.isCheckedChange, 'emit');
    fixture.detectChanges();

    component.toggleCheckbox();
    expect(component.isUnchecking).toBeTrue();

    tick(300); // Wait for timeout
    expect(component.isChecked).toBeFalse();
    expect(component.isUnchecking).toBeFalse();
    expect(component.isCheckedChange.emit).toHaveBeenCalledWith(false);
  }));

  it('should toggle from unchecked to checked for default variant', () => {
    component.variant = 'default';
    component.isChecked = false;
    spyOn(component.isCheckedChange, 'emit');

    component.toggleCheckbox();
    expect(component.isChecked).toBeTrue();
    expect(component.isCheckedChange.emit).toHaveBeenCalledWith(true);
  });

  it('should handle indeterminate toggle', () => {
    component.indeterminate = true;
    spyOn(component.isCheckedChange, 'emit');

    component.toggleCheckbox();
    expect(component.isChecked).toBeTrue();
    expect(component.indeterminate).toBeFalse();
    expect(component.isCheckedChange.emit).toHaveBeenCalledWith(true);
  });

  it('should handle animated variant - checking flow', fakeAsync(() => {
    component.variant = 'animated';
    component.isChecked = false;
    spyOn(component.isCheckedChange, 'emit');

    component.toggleCheckbox();
    expect(component.isAnimating).toBeTrue();
    tick(600);
    expect(component.isAnimating).toBeFalse();
    expect(component.isChecked).toBeTrue();
    expect(component.isCheckedChange.emit).toHaveBeenCalledWith(true);
  }));

  it('should handle animated variant - unchecking flow', fakeAsync(() => {
    component.variant = 'animated';
    component.isChecked = true;
    spyOn(component.isCheckedChange, 'emit');

    component.toggleCheckbox();
    expect(component.isUnchecking).toBeTrue();
    tick(300);
    expect(component.isChecked).toBeFalse();
    expect(component.isUnchecking).toBeFalse();
    expect(component.isCheckedChange.emit).toHaveBeenCalledWith(false);
  }));

  it('should handle with-bg variant - checking flow', () => {
    component.variant = 'with-bg';
    component.isChecked = false;
    spyOn(component.isCheckedChange, 'emit');

    component.toggleCheckbox();
    expect(component.isChecked).toBeTrue();
    expect(component.isCheckedChange.emit).toHaveBeenCalledWith(true);
  });

  it('should handle with-bg variant - unchecking flow', fakeAsync(() => {
    component.variant = 'with-bg';
    component.isChecked = true;
    spyOn(component.isCheckedChange, 'emit');

    component.toggleCheckbox();
    expect(component.isUnchecking).toBeTrue();
    tick(150);
    expect(component.isChecked).toBeFalse();
    expect(component.isUnchecking).toBeFalse();
    expect(component.isCheckedChange.emit).toHaveBeenCalledWith(false);
  }));

  it('should not toggle when disabled', () => {
    component.disable = true;
    spyOn(component.isCheckedChange, 'emit');

    component.toggleCheckbox();
    expect(component.isChecked).toBeFalse();
    expect(component.isCheckedChange.emit).not.toHaveBeenCalled();
  });

  it('should handle keyboard event - space key', () => {
    spyOn(component, 'toggleCheckbox');

    const event = new KeyboardEvent('keydown', { key: ' ' });
    component.onKeyDown(event);

    expect(component.toggleCheckbox).toHaveBeenCalled();
  });

  it('should handle keyboard event - enter key', () => {
    spyOn(component, 'toggleCheckbox');

    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    component.onKeyDown(event);

    expect(component.toggleCheckbox).toHaveBeenCalled();
  });

  it('should ignore keyboard key if not space/enter', () => {
    spyOn(component, 'toggleCheckbox');

    const event = new KeyboardEvent('keydown', { key: 'Escape' });
    component.onKeyDown(event);

    expect(component.toggleCheckbox).not.toHaveBeenCalled();
  });

  it('should compute container classes correctly', () => {
    component.variant = 'animated';
    component.size = 'large';
    component.disable = true;

    const classes = component.containerClasses;
    expect(classes['animated']).toBeTrue();
    expect(classes['large']).toBeTrue();
    expect(classes['disabled']).toBeTrue();
  });

  it('should compute checkbox classes correctly', () => {
    component.isChecked = true;
    component.isUnchecking = true;
    component.isAnimating = true;

    const classes = component.checkboxClasses;
    expect(classes['checked']).toBeFalse(); // Because isUnchecking
    expect(classes['unchecking']).toBeTrue();
  });

  it('should show checkmark only if not indeterminate', () => {
    component.isChecked = true;
    component.indeterminate = false;

    expect(component.showCheckmark).toBeTrue();

    component.indeterminate = true;
    expect(component.showCheckmark).toBeFalse();
  });

  it('should show icon if checked/indeterminate/unchecking', () => {
    component.isChecked = false;
    component.indeterminate = false;
    component.isUnchecking = false;

    expect(component.showIcon).toBeFalse();

    component.isChecked = true;
    expect(component.showIcon).toBeTrue();

    component.isChecked = false;
    component.indeterminate = true;
    expect(component.showIcon).toBeTrue();

    component.indeterminate = false;
    component.isUnchecking = true;
    expect(component.showIcon).toBeTrue();
  });

  it('should toggle when clicked (integration test)', () => {
    component.label = 'Accept';
    spyOn(component, 'toggleCheckbox');

    fixture.detectChanges();
    const box = fixture.debugElement.query(By.css('.ava-checkbox'));
    box.triggerEventHandler('click');

    expect(component.toggleCheckbox).toHaveBeenCalled();
  });

  it('should bind correct ARIA attributes', () => {
    component.label = 'Agree';
    component.isChecked = true;
    component.indeterminate = false;
    component.disable = false;
    fixture.detectChanges();

    const box = fixture.debugElement.query(By.css('.ava-checkbox')).nativeElement;

    expect(box.getAttribute('aria-label')).toBe('Agree');
    expect(box.getAttribute('aria-checked')).toBe('true');
    expect(box.getAttribute('aria-disabled')).toBe('false');
    expect(box.getAttribute('role')).toBe('checkbox');
  });
});
