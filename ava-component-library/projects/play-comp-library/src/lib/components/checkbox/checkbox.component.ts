import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
 ViewEncapsulation} from '@angular/core';

@Component({
  selector: 'ava-checkbox',
  imports: [CommonModule],
  templateUrl: './checkbox.component.html',
  styleUrl: './checkbox.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation:ViewEncapsulation.None,
})
export class CheckboxComponent {
  @Input() variant: 'default' | 'with-bg' | 'animated' = 'default';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() alignment: 'vertical' | 'horizontal' = 'vertical';
  @Input() label = '';
  @Input() isChecked = false;
  @Input() indeterminate = false;
  @Input() disable = false;

  @Output() isCheckedChange = new EventEmitter<boolean>();

  isAnimating = false;
  isUnchecking = false;

  // Getter for container classes
  get containerClasses(): Record<string, boolean> {
    return {
      'with-bg': this.variant === 'with-bg',
      animated: this.variant === 'animated',
      horizontal: this.alignment === 'horizontal',
      small: this.size === 'small',
      medium: this.size === 'medium',
      large: this.size === 'large',
      disabled: this.disable,
    };
  }

  // Getter for checkbox classes
  get checkboxClasses(): Record<string, boolean> {
    return {
      checked: this.isChecked && !this.isUnchecking,
      indeterminate: this.indeterminate,
      checking: this.isAnimating && this.isChecked,
      unchecking: this.isUnchecking,
    };
  }

  // Getter for showing icon
  get showIcon(): boolean {
    return this.isChecked || this.indeterminate || this.isUnchecking;
  }

  // Getter for showing checkmark
  get showCheckmark(): boolean {
    return (this.isChecked || this.isUnchecking) && !this.indeterminate;
  }

  toggleCheckbox(): void {
    if (this.disable) return;

    if (this.indeterminate) {
      this.isChecked = true;
      this.indeterminate = false;
      this.isCheckedChange.emit(this.isChecked);
      return;
    }

    if (this.variant === 'animated') {
      if (this.isChecked) {
        this.handleUnchecking();
      } else {
        this.handleChecking();
      }
    } else if (this.variant === 'with-bg') {
      if (this.isChecked) {
        this.handleWithBgUnchecking();
      } else {
        this.isChecked = true;
        this.isCheckedChange.emit(this.isChecked);
      }
    } else {
      // Default variant
      if (this.isChecked) {
        this.isUnchecking = true;

        setTimeout(() => {
          this.isChecked = false;
          this.isUnchecking = false;
          this.isCheckedChange.emit(this.isChecked);
        }, 300); // Wait for erase animation (300ms)
      } else {
        this.isChecked = true;
        this.isCheckedChange.emit(this.isChecked);
      }
    }
  }

  // Keyboard accessibility
  onKeyDown(event: KeyboardEvent): void {
    // Handle Space and Enter keys to toggle checkbox
    if (event.key === ' ' || event.key === 'Enter') {
      event.preventDefault(); // Prevent default scrolling behavior for Space
      this.toggleCheckbox();
    }
  }

  private handleChecking(): void {
    this.isAnimating = true;
    this.isChecked = true;

    setTimeout(() => {
      this.isAnimating = false;
      this.isCheckedChange.emit(this.isChecked);
    }, 600); // Background fill (300ms) + checkmark draw (150ms) + delay (300ms)
  }

  private handleUnchecking(): void {
    this.isUnchecking = true;

    setTimeout(() => {
      this.isChecked = false;
      this.isUnchecking = false;
      this.isCheckedChange.emit(this.isChecked);
    }, 300); // Both background empty and checkmark erase (300ms)
  }

  private handleWithBgUnchecking(): void {
    this.isUnchecking = true;

    setTimeout(() => {
      this.isChecked = false;
      this.isUnchecking = false;
      this.isCheckedChange.emit(this.isChecked);
    }, 150); // Both background transition and checkmark erase (150ms)
  }
}
