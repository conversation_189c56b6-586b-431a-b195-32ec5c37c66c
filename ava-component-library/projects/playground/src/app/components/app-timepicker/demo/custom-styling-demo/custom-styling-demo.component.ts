import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TimePickerComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-custom-styling-demo',
  standalone: true,
  imports: [CommonModule, TimePickerComponent],
  templateUrl: './custom-styling-demo.component.html',
  styleUrls: ['./custom-styling-demo.component.scss'],
})
export class CustomStylingDemoComponent {
  selectedTime = '';
  currentTheme = 'default';

  onTimeSelected(time: string) {
    this.selectedTime = time;
    console.log('Selected time:', time);
  }

  setTheme(theme: string) {
    this.currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
  }
}
