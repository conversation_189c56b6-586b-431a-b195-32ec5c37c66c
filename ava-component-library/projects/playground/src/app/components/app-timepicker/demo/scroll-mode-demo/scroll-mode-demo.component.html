<div class="demo-container">
  <div class="demo-header">
    <h2>Scroll Mode Interface</h2>
    <p>
      Interactive scroll-based time selection with smooth animations and visual
      feedback.
    </p>
  </div>

  <div class="demo-section">
    <div class="demo-example">
      <h3>Scroll-Based Selection</h3>
      <p>
        Click on the time picker to open the scroll interface. Use mouse wheel
        or drag to scroll through time values.
      </p>

      <div class="time-picker-wrapper">
        <ava-time-picker
          (timeSelected)="onTimeSelected($event)"
        ></ava-time-picker>
      </div>

      <div class="demo-output" *ngIf="selectedTime">
        <h4>Selected Time:</h4>
        <p class="selected-time">{{ selectedTime }}</p>
      </div>

      <div class="scroll-events" *ngIf="scrollEvents.length > 0">
        <h4>Recent Scroll Events:</h4>
        <ul>
          <li *ngFor="let event of scrollEvents">{{ event }}</li>
        </ul>
      </div>
    </div>

    <div class="demo-code">
      <h3>Code Example</h3>
      <pre><code>&lt;ava-time-picker 
  (timeSelected)="onTimeSelected($event)"&gt;
&lt;/ava-time-picker&gt;</code></pre>

      <h4>Scroll Features</h4>
      <ul>
        <li><strong>Mouse Wheel:</strong> Scroll through time values</li>
        <li><strong>Drag Scrolling:</strong> Click and drag to scroll</li>
        <li>
          <strong>Smooth Animation:</strong> Hardware-accelerated transitions
        </li>
        <li><strong>Visual Feedback:</strong> Clear selection indicators</li>
        <li>
          <strong>Boundary Enforcement:</strong> Prevents invalid selections
        </li>
      </ul>
    </div>
  </div>

  <div class="demo-features">
    <h3>Scroll Mode Features</h3>
    <div class="features-grid">
      <div class="feature-card">
        <h4>🖱️ Mouse Wheel Support</h4>
        <p>
          Use your mouse wheel to scroll through hours, minutes, and AM/PM
          periods smoothly.
        </p>
      </div>

      <div class="feature-card">
        <h4>👆 Touch Support</h4>
        <p>
          Optimized for touch devices with gesture recognition and momentum
          scrolling.
        </p>
      </div>

      <div class="feature-card">
        <h4>🎯 Visual Feedback</h4>
        <p>
          Clear selection indicators and hover states provide immediate visual
          feedback.
        </p>
      </div>

      <div class="feature-card">
        <h4>⚡ Smooth Animation</h4>
        <p>
          Hardware-accelerated scroll animations with momentum for natural feel.
        </p>
      </div>

      <div class="feature-card">
        <h4>🔒 Boundary Enforcement</h4>
        <p>
          Prevents scrolling beyond valid time ranges (01-12 hours, 00-59
          minutes).
        </p>
      </div>

      <div class="feature-card">
        <h4>📐 Padding System</h4>
        <p>Intelligent padding for optimal centering and selection accuracy.</p>
      </div>
    </div>
  </div>
</div>
