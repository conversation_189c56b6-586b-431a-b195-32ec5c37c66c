.demo-container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 2rem;
  text-align: center;

  h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 1.125rem;
    line-height: 1.6;
  }
}

.demo-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.demo-example {
  padding: 2rem;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
}

.time-picker-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
}

.demo-output {
  margin-top: 1.5rem;
  padding: 1rem;

  h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .selected-time {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-brand-primary, #e91e63);
    margin: 0;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  }
}

.demo-code {
  background: var(--color-surface-secondary, #f8fafc);
  border: 1px solid var(--color-border-default, #e2e8f0);
  border-radius: var(--global-radius-lg, 0.75rem);
  padding: 1.5rem;

  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 1rem;
  }

  pre {
    background: var(--color-surface-tertiary, #f1f5f9);
    border: 1px solid var(--color-border-subtle, #e2e8f0);
    border-radius: var(--global-radius-md, 0.5rem);
    padding: 1rem;
    overflow-x: auto;
    margin: 0;

    code {
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      font-size: 0.875rem;
      line-height: 1.5;
      color: var(--color-text-primary, #1a202c);
    }
  }
}

.demo-features {
  background: var(--color-background-primary, #ffffff);
  border: 1px solid var(--color-border-default, #e2e8f0);
  border-radius: var(--global-radius-lg, 0.75rem);
  padding: 2rem;
  box-shadow: var(--global-elevation-01);

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 1rem;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: 0.75rem 0;
      border-bottom: 1px solid var(--color-border-subtle, #f1f5f9);
      color: var(--color-text-secondary, #64748b);
      line-height: 1.6;

      &:last-child {
        border-bottom: none;
      }

      strong {
        color: var(--color-text-primary, #1a202c);
        font-weight: 600;
      }
    }
  }
}
