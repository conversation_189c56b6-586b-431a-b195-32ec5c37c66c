import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TimePickerComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-validation-demo',
  standalone: true,
  imports: [CommonModule, TimePickerComponent],
  templateUrl: './validation-demo.component.html',
  styleUrls: ['./validation-demo.component.scss'],
})
export class ValidationDemoComponent {
  selectedTime = '';
  validationMessages: string[] = [];

  onTimeSelected(time: string) {
    this.selectedTime = time;
    console.log('Selected time:', time);
  }

  onValidationEvent(message: string) {
    this.validationMessages.unshift(
      `Validation: ${message} - ${new Date().toLocaleTimeString()}`
    );
    if (this.validationMessages.length > 5) {
      this.validationMessages.pop();
    }
  }
}
