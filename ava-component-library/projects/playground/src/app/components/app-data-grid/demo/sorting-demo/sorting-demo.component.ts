import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataGridComponent } from '../../../../../../../play-comp-library/src/lib/components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-cell-def.directive';

@Component({
  selector: 'app-sorting-demo',
  imports: [
    CommonModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
  ],
  templateUrl: './sorting-demo.component.html',
  styleUrl: './sorting-demo.component.scss',
})
export class SortingDemoComponent {
  employeeData = [
    {
      id: 1,
      name: 'Alice Johnson',
      position: 'Senior Developer',
      salary: 95000,
      joinDate: '2020-03-15',
      experience: 8,
      department: 'Engineering',
    },
    {
      id: 2,
      name: 'Bob Smith',
      position: 'Marketing Manager',
      salary: 75000,
      joinDate: '2019-07-22',
      experience: 6,
      department: 'Marketing',
    },
    {
      id: 3,
      name: 'Carlos Martinez',
      position: 'Sales Representative',
      salary: 55000,
      joinDate: '2021-11-08',
      experience: 3,
      department: 'Sales',
    },
    {
      id: 4,
      name: 'Diana Lee',
      position: 'UX Designer',
      salary: 70000,
      joinDate: '2020-09-12',
      experience: 5,
      department: 'Design',
    },
    {
      id: 5,
      name: 'Ethan Brown',
      position: 'Data Analyst',
      salary: 65000,
      joinDate: '2022-01-30',
      experience: 2,
      department: 'Analytics',
    },
    {
      id: 6,
      name: 'Fiona Green',
      position: 'Project Manager',
      salary: 85000,
      joinDate: '2018-05-10',
      experience: 9,
      department: 'Operations',
    },
    {
      id: 7,
      name: 'George Wang',
      position: 'DevOps Engineer',
      salary: 90000,
      joinDate: '2019-12-03',
      experience: 7,
      department: 'Engineering',
    },
    {
      id: 8,
      name: 'Hannah Kim',
      position: 'Content Writer',
      salary: 45000,
      joinDate: '2021-08-15',
      experience: 1,
      department: 'Marketing',
    },
  ];

  displayedColumns = ['name', 'position', 'salary', 'experience', 'joinDate'];

  salesData = [
    { month: 'January', revenue: 125000, orders: 340, conversion: 3.2 },
    { month: 'February', revenue: 135000, orders: 385, conversion: 3.8 },
    { month: 'March', revenue: 142000, orders: 420, conversion: 4.1 },
    { month: 'April', revenue: 128000, orders: 365, conversion: 3.5 },
    { month: 'May', revenue: 155000, orders: 445, conversion: 4.3 },
    { month: 'June', revenue: 168000, orders: 478, conversion: 4.6 },
  ];

  salesColumns = ['month', 'revenue', 'orders', 'conversion'];

  codeExample = `<ava-data-grid [dataSource]="data" [displayedColumns]="columns">
  
  <!-- Sortable text column -->
  <ng-container avaColumnDef="name" [sortable]="true">
    <ng-container *avaHeaderCellDef>Name</ng-container>
    <ng-container *avaCellDef="let row">{{ row.name }}</ng-container>
  </ng-container>
  
  <!-- Sortable number column -->
  <ng-container avaColumnDef="salary" [sortable]="true">
    <ng-container *avaHeaderCellDef>Salary</ng-container>
    <ng-container *avaCellDef="let row">
      {{ row.salary | currency }}
    </ng-container>
  </ng-container>
  
  <!-- Sortable date column -->
  <ng-container avaColumnDef="joinDate" [sortable]="true">
    <ng-container *avaHeaderCellDef>Join Date</ng-container>
    <ng-container *avaCellDef="let row">
      {{ row.joinDate | date }}
    </ng-container>
  </ng-container>
  
</ava-data-grid>`;
}
