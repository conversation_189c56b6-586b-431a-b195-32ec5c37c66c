.demo-content {
  padding: var(--global-spacing-6, 3rem) 0;
}

.demo-section {
  margin-bottom: var(--global-spacing-8, 4rem);

  h2 {
    font-size: var(--global-font-size-2xl, 1.5rem);
    font-weight: var(--global-font-weight-semibold, 600);
    margin-bottom: var(--global-spacing-2, 0.5rem);
    color: var(--color-text-primary);
  }

  p {
    color: var(--color-text-secondary);
    margin-bottom: var(--global-spacing-4, 1.5rem);
    line-height: var(--global-line-height-normal, 1.5);
  }
}

.demo-card {
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  overflow: hidden;
  margin-bottom: var(--global-spacing-6, 3rem);
}

.card-header {
  padding: var(--global-spacing-4, 1.5rem);
  border-bottom: 1px solid var(--color-border-subtle);
  background-color: var(--color-background-secondary);

  h3 {
    font-size: var(--global-font-size-lg, 1.125rem);
    font-weight: var(--global-font-weight-semibold, 600);
    margin: 0 0 var(--global-spacing-1, 0.25rem) 0;
    color: var(--color-text-primary);
  }

  p {
    font-size: var(--global-font-size-sm, 0.875rem);
    color: var(--color-text-secondary);
    margin: 0;
  }
}

.card-content {
  padding: var(--global-spacing-4, 1.5rem);
}

// Custom cell styles
.user-cell {
  display: flex;
  align-items: center;
  gap: var(--global-spacing-3, 0.75rem);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: var(--global-spacing-1, 0.25rem);
}

.user-name {
  font-weight: var(--global-font-weight-medium, 500);
  color: var(--color-text-primary);
}

.date-cell,
.budget-cell,
.deadline-cell,
.project-cell {
  display: flex;
  align-items: center;
  gap: var(--global-spacing-2, 0.5rem);
}

.overdue {
  color: var(--color-danger-emphasis) !important;
}

.permission-cell {
  display: flex;
  align-items: center;
  gap: var(--global-spacing-2, 0.5rem);
}

.permission-bar {
  width: 60px;
  height: 8px;
  background-color: var(--color-neutral-subtle);
  border-radius: var(--global-radius-sm, 4px);
  overflow: hidden;
}

.permission-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--color-success-emphasis),
    var(--color-primary-emphasis)
  );
  transition: width var(--global-motion-duration-standard, 0.15s)
    var(--global-motion-easing-standard, ease);
}

.permission-text {
  font-size: var(--global-font-size-sm, 0.875rem);
  color: var(--color-text-secondary);
  min-width: 30px;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: var(--global-spacing-2, 0.5rem);
  min-width: 120px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: var(--color-neutral-subtle);
  border-radius: var(--global-radius-sm, 4px);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--color-primary-emphasis),
    var(--color-success-emphasis)
  );
  transition: width var(--global-motion-duration-standard, 0.15s)
    var(--global-motion-easing-standard, ease);
}

.progress-text {
  font-size: var(--global-font-size-sm, 0.875rem);
  font-weight: var(--global-font-weight-medium, 500);
  color: var(--color-text-primary);
  min-width: 40px;
}

.team-cell {
  display: flex;
  align-items: center;
  gap: var(--global-spacing-1, 0.25rem);
}

.team-count {
  font-size: var(--global-font-size-sm, 0.875rem);
  color: var(--color-text-secondary);
  margin-left: var(--global-spacing-2, 0.5rem);
}

.project-name {
  font-weight: var(--global-font-weight-medium, 500);
  color: var(--color-text-primary);
}

.action-buttons {
  display: flex;
  gap: var(--global-spacing-1, 0.25rem);
  align-items: center;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--global-spacing-4, 1.5rem);
  margin-top: var(--global-spacing-4, 1.5rem);
}

.feature-card {
  padding: var(--global-spacing-4, 1.5rem);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  background-color: var(--color-background-secondary);

  h4 {
    font-size: var(--global-font-size-base, 1rem);
    font-weight: var(--global-font-weight-semibold, 600);
    margin: 0 0 var(--global-spacing-2, 0.5rem) 0;
    color: var(--color-text-primary);
  }

  p {
    font-size: var(--global-font-size-sm, 0.875rem);
    color: var(--color-text-secondary);
    margin: 0;
    line-height: var(--global-line-height-normal, 1.5);
  }
}

.code-block {
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  padding: var(--global-spacing-4, 1.5rem);
  overflow-x: auto;

  pre {
    margin: 0;
    font-family: var(--global-font-family-mono, "Courier New", monospace);
    font-size: var(--global-font-size-sm, 0.875rem);
    line-height: var(--global-line-height-normal, 1.5);
    color: var(--color-text-primary);
  }

  code {
    white-space: pre;
  }
}
