import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataGridComponent } from '../../../../../../../play-comp-library/src/lib/components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-cell-def.directive';

@Component({
  selector: 'app-filtering-demo',
  imports: [
    CommonModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
  ],
  templateUrl: './filtering-demo.component.html',
  styleUrl: './filtering-demo.component.scss',
})
export class FilteringDemoComponent {
  customerData = [
    {
      id: 1,
      name: 'Alice Johnson',
      email: '<EMAIL>',
      company: 'TechCorp Inc.',
      status: 'Active',
      location: 'New York',
      industry: 'Technology',
      revenue: 250000,
    },
    {
      id: 2,
      name: 'Bob Smith',
      email: '<EMAIL>',
      company: 'Retail Solutions Co.',
      status: 'Inactive',
      location: 'Los Angeles',
      industry: 'Retail',
      revenue: 180000,
    },
    {
      id: 3,
      name: 'Carlos Martinez',
      email: '<EMAIL>',
      company: 'Manufacturing Plus',
      status: 'Pending',
      location: 'Chicago',
      industry: 'Manufacturing',
      revenue: 320000,
    },
    {
      id: 4,
      name: 'Diana Lee',
      email: '<EMAIL>',
      company: 'HealthSys Group',
      status: 'Active',
      location: 'Houston',
      industry: 'Healthcare',
      revenue: 420000,
    },
    {
      id: 5,
      name: 'Ethan Brown',
      email: '<EMAIL>',
      company: 'Finance Solutions',
      status: 'Active',
      location: 'Miami',
      industry: 'Finance',
      revenue: 380000,
    },
    {
      id: 6,
      name: 'Fiona Green',
      email: '<EMAIL>',
      company: 'Education First',
      status: 'Inactive',
      location: 'Seattle',
      industry: 'Education',
      revenue: 95000,
    },
    {
      id: 7,
      name: 'George Wang',
      email: '<EMAIL>',
      company: 'Consulting Experts',
      status: 'Active',
      location: 'Boston',
      industry: 'Consulting',
      revenue: 275000,
    },
    {
      id: 8,
      name: 'Hannah Kim',
      email: '<EMAIL>',
      company: 'Media Productions',
      status: 'Pending',
      location: 'San Francisco',
      industry: 'Media',
      revenue: 150000,
    },
    {
      id: 9,
      name: 'Ian Davis',
      email: '<EMAIL>',
      company: 'Logistics Express',
      status: 'Active',
      location: 'Denver',
      industry: 'Logistics',
      revenue: 200000,
    },
    {
      id: 10,
      name: 'Julia Roberts',
      email: '<EMAIL>',
      company: 'Real Estate Pros',
      status: 'Active',
      location: 'Phoenix',
      industry: 'Real Estate',
      revenue: 310000,
    },
  ];

  displayedColumns = [
    'name',
    'email',
    'company',
    'status',
    'location',
    'industry',
  ];

  inventoryData = [
    {
      sku: 'TECH-001',
      product: 'Wireless Mouse',
      category: 'Electronics',
      stock: 150,
      price: 29.99,
    },
    {
      sku: 'TECH-002',
      product: 'Bluetooth Keyboard',
      category: 'Electronics',
      stock: 85,
      price: 79.99,
    },
    {
      sku: 'BOOK-001',
      product: 'JavaScript Handbook',
      category: 'Books',
      stock: 45,
      price: 34.95,
    },
    {
      sku: 'FURN-001',
      product: 'Ergonomic Chair',
      category: 'Furniture',
      stock: 12,
      price: 299.99,
    },
    {
      sku: 'TECH-003',
      product: 'USB-C Cable',
      category: 'Electronics',
      stock: 200,
      price: 14.99,
    },
    {
      sku: 'BOOK-002',
      product: 'Design Principles',
      category: 'Books',
      stock: 28,
      price: 42.5,
    },
    {
      sku: 'FURN-002',
      product: 'Standing Desk',
      category: 'Furniture',
      stock: 8,
      price: 449.99,
    },
    {
      sku: 'TECH-004',
      product: 'Laptop Stand',
      category: 'Electronics',
      stock: 67,
      price: 89.99,
    },
  ];

  inventoryColumns = ['sku', 'product', 'category', 'stock', 'price'];

  codeExample = `<ava-data-grid [dataSource]="data" [displayedColumns]="columns">
  
  <!-- Text filtering -->
  <ng-container avaColumnDef="name" [filter]="true" [sortable]="true">
    <ng-container *avaHeaderCellDef>Name</ng-container>
    <ng-container *avaCellDef="let row">{{ row.name }}</ng-container>
  </ng-container>
  
  <!-- Email filtering -->
  <ng-container avaColumnDef="email" [filter]="true" [sortable]="true">
    <ng-container *avaHeaderCellDef>Email</ng-container>
    <ng-container *avaCellDef="let row">{{ row.email }}</ng-container>
  </ng-container>
  
  <!-- Status filtering -->
  <ng-container avaColumnDef="status" [filter]="true">
    <ng-container *avaHeaderCellDef>Status</ng-container>
    <ng-container *avaCellDef="let row">{{ row.status }}</ng-container>
  </ng-container>
  
</ava-data-grid>`;
}
