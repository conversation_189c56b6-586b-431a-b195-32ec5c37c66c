<div class="demo-content">
  <div class="demo-section">
    <div class="demo-card">
      <div class="card-content">
        <ava-data-grid
          [dataSource]="inventoryData"
          [displayedColumns]="inventoryColumns"
        >
          <ng-container avaColumnDef="sku" [filter]="true" [sortable]="true">
            <ng-container *avaHeaderCellDef>SKU</ng-container>
            <ng-container *avaCellDef="let row">
              <code class="sku-code">{{ row.sku }}</code>
            </ng-container>
          </ng-container>

          <ng-container
            avaColumnDef="product"
            [filter]="true"
            [sortable]="true"
          >
            <ng-container *avaHeaderCellDef>Product Name</ng-container>
            <ng-container *avaCellDef="let row">{{ row.product }}</ng-container>
          </ng-container>

          <ng-container
            avaColumnDef="category"
            [filter]="true"
            [sortable]="true"
          >
            <ng-container *avaHeaderCellDef>Category</ng-container>
            <ng-container *avaCellDef="let row">
              <span class="category-tag">{{ row.category }}</span>
            </ng-container>
          </ng-container>

          <ng-container avaColumnDef="stock" [filter]="true" [sortable]="true">
            <ng-container *avaHeaderCellDef>Stock</ng-container>
            <ng-container *avaCellDef="let row">
              <span
                class="stock-indicator"
                [class]="row.stock < 50 ? 'low-stock' : 'normal-stock'"
              >
                {{ row.stock }} units
              </span>
            </ng-container>
          </ng-container>

          <ng-container avaColumnDef="price" [filter]="true" [sortable]="true">
            <ng-container *avaHeaderCellDef>Price</ng-container>
            <ng-container *avaCellDef="let row">${{ row.price }}</ng-container>
          </ng-container>
        </ava-data-grid>
      </div>
    </div>
  </div>
</div>
