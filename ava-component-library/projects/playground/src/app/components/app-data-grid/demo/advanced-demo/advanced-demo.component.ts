import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DataGridComponent } from '../../../../../../../play-comp-library/src/lib/components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-cell-def.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { BadgesComponent } from '../../../../../../../play-comp-library/src/lib/components/badges/badges.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { AvatarsComponent } from '../../../../../../../play-comp-library/src/lib/components/avatars/avatars.component';
import { CheckboxComponent } from '../../../../../../../play-comp-library/src/lib/components/checkbox/checkbox.component';
import { AvaTextboxComponent } from '../../../../../../../play-comp-library/src/lib/components/textbox/ava-textbox.component';
import { SelectComponent } from '../../../../../../../play-comp-library/src/lib/components/select/select.component';
import { SelectOptionComponent } from '../../../../../../../play-comp-library/src/lib/components/select/select-option/select-option.component';

@Component({
  selector: 'app-advanced-demo',
  imports: [
    CommonModule,
    FormsModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
    ButtonComponent,
    BadgesComponent,
    IconComponent,
    AvatarsComponent,
    CheckboxComponent,
    AvaTextboxComponent,
    SelectComponent,
    SelectOptionComponent,
  ],
  templateUrl: './advanced-demo.component.html',
  styleUrl: './advanced-demo.component.scss',
})
export class AdvancedDemoComponent {
  originalData = [
    {
      id: 1,
      select: false,
      avatar: 'JS',
      name: 'John Smith',
      email: '<EMAIL>',
      department: 'Engineering',
      position: 'Senior Developer',
      salary: 95000,
      startDate: '2019-03-15',
      performance: 4.8,
      projects: ['Mobile App', 'Web Portal'],
      skills: ['React', 'Node.js', 'TypeScript'],
      status: 'Active',
      manager: 'Alice Johnson',
      location: 'New York',
      editable: false,
    },
    {
      id: 2,
      select: false,
      avatar: 'MJ',
      name: 'Maria Garcia',
      email: '<EMAIL>',
      department: 'Marketing',
      position: 'Marketing Manager',
      salary: 78000,
      startDate: '2020-07-22',
      performance: 4.6,
      projects: ['Brand Campaign', 'Social Media'],
      skills: ['Digital Marketing', 'Analytics', 'Content Strategy'],
      status: 'Active',
      manager: 'Robert Chen',
      location: 'Los Angeles',
      editable: false,
    },
    {
      id: 3,
      select: false,
      avatar: 'DK',
      name: 'David Kim',
      email: '<EMAIL>',
      department: 'Engineering',
      position: 'DevOps Engineer',
      salary: 88000,
      startDate: '2021-01-10',
      performance: 4.7,
      projects: ['Infrastructure', 'CI/CD Pipeline'],
      skills: ['AWS', 'Docker', 'Kubernetes'],
      status: 'Active',
      manager: 'Alice Johnson',
      location: 'Seattle',
      editable: false,
    },
    {
      id: 4,
      select: false,
      avatar: 'SB',
      name: 'Sarah Brown',
      email: '<EMAIL>',
      department: 'Design',
      position: 'UX Designer',
      salary: 72000,
      startDate: '2020-11-05',
      performance: 4.9,
      projects: ['Mobile App', 'Design System'],
      skills: ['Figma', 'User Research', 'Prototyping'],
      status: 'On Leave',
      manager: 'Emma Wilson',
      location: 'Austin',
      editable: false,
    },
    {
      id: 5,
      select: false,
      avatar: 'TC',
      name: 'Tom Chen',
      email: '<EMAIL>',
      department: 'Sales',
      position: 'Sales Director',
      salary: 105000,
      startDate: '2018-09-12',
      performance: 4.5,
      projects: ['Enterprise Sales', 'Partner Relations'],
      skills: ['Sales Strategy', 'CRM', 'Negotiation'],
      status: 'Active',
      manager: 'CEO',
      location: 'Chicago',
      editable: false,
    },
  ];

  employeeData = [...this.originalData];
  selectedCount = 0;
  sortedData: any[] = [];

  displayedColumns = [
    'select',
    'employee',
    'department',
    'position',
    'salary',
    'performance',
    'status',
    'actions',
  ];

  departments = ['All', 'Engineering', 'Marketing', 'Design', 'Sales'];
  statusOptions = ['All', 'Active', 'On Leave', 'Inactive'];

  selectedDepartment = 'All';
  selectedStatus = 'All';
  searchTerm = '';

  onSelectionChange() {
    this.selectedCount = this.employeeData.filter((emp) => emp.select).length;
  }

  toggleAll(checked: boolean) {
    this.employeeData.forEach((emp) => (emp.select = checked));
    this.onSelectionChange();
  }

  onEdit(employee: any) {
    employee.editable = !employee.editable;
  }

  onSave(employee: any) {
    employee.editable = false;
    console.log('Saved employee:', employee);
  }

  onCancel(employee: any) {
    // Reset to original data
    const original = this.originalData.find((orig) => orig.id === employee.id);
    if (original) {
      Object.assign(employee, original);
    }
    employee.editable = false;
  }

  onDelete(employee: any) {
    if (confirm(`Are you sure you want to delete ${employee.name}?`)) {
      this.employeeData = this.employeeData.filter(
        (emp) => emp.id !== employee.id
      );
      this.onSelectionChange();
    }
  }

  onExport() {
    const selected = this.employeeData.filter((emp) => emp.select);
    console.log(
      'Exporting:',
      selected.length > 0 ? selected : this.employeeData
    );
  }

  onBulkDelete() {
    const selected = this.employeeData.filter((emp) => emp.select);
    if (
      selected.length > 0 &&
      confirm(`Delete ${selected.length} selected employees?`)
    ) {
      this.employeeData = this.employeeData.filter((emp) => !emp.select);
      this.onSelectionChange();
    }
  }

  applyFilters() {
    let filtered = [...this.originalData];

    if (this.selectedDepartment !== 'All') {
      filtered = filtered.filter(
        (emp) => emp.department === this.selectedDepartment
      );
    }

    if (this.selectedStatus !== 'All') {
      filtered = filtered.filter((emp) => emp.status === this.selectedStatus);
    }

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (emp) =>
          emp.name.toLowerCase().includes(term) ||
          emp.email.toLowerCase().includes(term) ||
          emp.position.toLowerCase().includes(term)
      );
    }

    this.employeeData = filtered.map((emp) => ({ ...emp, select: false }));
    this.onSelectionChange();
  }

  clearFilters() {
    this.selectedDepartment = 'All';
    this.selectedStatus = 'All';
    this.searchTerm = '';
    this.applyFilters();
  }

  onDataSorted(sortedData: any[]) {
    this.sortedData = sortedData;
  }

  getStatusState(
    status: string
  ):
    | 'high-priority'
    | 'medium-priority'
    | 'low-priority'
    | 'neutral'
    | 'information' {
    switch (status) {
      case 'Active':
        return 'low-priority';
      case 'On Leave':
        return 'medium-priority';
      case 'Inactive':
        return 'neutral';
      default:
        return 'neutral';
    }
  }

  getPerformanceColor(performance: number): string {
    if (performance >= 4.5) return 'var(--color-success-emphasis)';
    if (performance >= 4.0) return 'var(--color-warning-emphasis)';
    return 'var(--color-danger-emphasis)';
  }

  formatSalary(salary: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(salary);
  }

  codeExample = `<!-- Advanced table with selection, inline editing, and bulk actions -->
<div class="table-controls">
  <div class="filters">
    <ava-textbox [(ngModel)]="searchTerm" placeholder="Search employees..."></ava-textbox>
    <ava-select [(ngModel)]="selectedDepartment" (ngModelChange)="applyFilters()">
      <ava-select-option value="All">All Departments</ava-select-option>
      <!-- ... more options -->
    </ava-select>
  </div>
  <div class="bulk-actions" *ngIf="selectedCount > 0">
    <ava-button (userClick)="onExport()">Export Selected</ava-button>
    <ava-button variant="danger" (userClick)="onBulkDelete()">Delete Selected</ava-button>
  </div>
</div>

<ava-data-grid [dataSource]="employeeData" [displayedColumns]="displayedColumns">
  
  <!-- Selection column -->
  <ng-container avaColumnDef="select">
    <ng-container *avaHeaderCellDef>
      <ava-checkbox (checkedChange)="toggleAll($event)"></ava-checkbox>
    </ng-container>
    <ng-container *avaCellDef="let row">
      <ava-checkbox [(checked)]="row.select" (checkedChange)="onSelectionChange()"></ava-checkbox>
    </ng-container>
  </ng-container>
  
  <!-- Inline editing cell -->
  <ng-container avaColumnDef="salary" [sortable]="true">
    <ng-container *avaHeaderCellDef>Salary</ng-container>
    <ng-container *avaCellDef="let row">
      <span *ngIf="!row.editable">{{ formatSalary(row.salary) }}</span>
      <ava-textbox *ngIf="row.editable" [(ngModel)]="row.salary" type="number"></ava-textbox>
    </ng-container>
  </ng-container>
  
</ava-data-grid>`;
}
