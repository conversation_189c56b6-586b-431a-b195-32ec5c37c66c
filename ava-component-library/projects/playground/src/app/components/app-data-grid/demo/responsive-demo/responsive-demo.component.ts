import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataGridComponent } from '../../../../../../../play-comp-library/src/lib/components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-cell-def.directive';
import { BadgesComponent } from '../../../../../../../play-comp-library/src/lib/components/badges/badges.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'app-responsive-demo',
  imports: [
    CommonModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
    BadgesComponent,
    IconComponent,
  ],
  templateUrl: './responsive-demo.component.html',
  styleUrl: './responsive-demo.component.scss',
})
export class ResponsiveDemoComponent {
  wideData = [
    {
      id: 1,
      productCode: 'PROD-001',
      name: 'Wireless Bluetooth Headphones',
      category: 'Electronics',
      brand: 'TechSound',
      model: 'WH-1000XM4',
      price: 299.99,
      stock: 45,
      location: 'Warehouse A',
      supplier: 'Global Tech Supplies',
      lastUpdated: '2024-01-15',
      status: 'In Stock',
    },
    {
      id: 2,
      productCode: 'PROD-002',
      name: 'Gaming Mechanical Keyboard',
      category: 'Electronics',
      brand: 'GamePro',
      model: 'RGB-MK87',
      price: 129.99,
      stock: 23,
      location: 'Warehouse B',
      supplier: 'Gaming Gear Ltd',
      lastUpdated: '2024-01-14',
      status: 'Low Stock',
    },
    {
      id: 3,
      productCode: 'PROD-003',
      name: 'Ergonomic Office Chair',
      category: 'Furniture',
      brand: 'ComfortPlus',
      model: 'ERG-2024',
      price: 449.99,
      stock: 12,
      location: 'Warehouse C',
      supplier: 'Office Solutions Inc',
      lastUpdated: '2024-01-13',
      status: 'In Stock',
    },
  ];

  mobileData = [
    { id: 1, name: 'John Doe', role: 'Admin', status: 'Active' },
    { id: 2, name: 'Jane Smith', role: 'User', status: 'Inactive' },
    { id: 3, name: 'Bob Johnson', role: 'Editor', status: 'Active' },
  ];

  wideColumns = [
    'productCode',
    'name',
    'category',
    'brand',
    'model',
    'price',
    'stock',
    'location',
    'supplier',
    'lastUpdated',
    'status',
  ];
  mobileColumns = ['name', 'role', 'status'];

  getStatusState(
    status: string
  ):
    | 'high-priority'
    | 'medium-priority'
    | 'low-priority'
    | 'neutral'
    | 'information' {
    switch (status) {
      case 'In Stock':
        return 'low-priority';
      case 'Low Stock':
        return 'medium-priority';
      case 'Out of Stock':
        return 'high-priority';
      case 'Active':
        return 'low-priority';
      case 'Inactive':
        return 'neutral';
      default:
        return 'neutral';
    }
  }

  codeExample = `<!-- The data grid automatically handles horizontal scrolling -->
<ava-data-grid [dataSource]="wideData" [displayedColumns]="wideColumns">
  <!-- Many columns will scroll horizontally on smaller screens -->
</ava-data-grid>

<!-- For mobile, consider fewer columns -->
<ava-data-grid [dataSource]="mobileData" [displayedColumns]="mobileColumns">
  <!-- Fewer columns for better mobile experience -->
</ava-data-grid>`;
}
