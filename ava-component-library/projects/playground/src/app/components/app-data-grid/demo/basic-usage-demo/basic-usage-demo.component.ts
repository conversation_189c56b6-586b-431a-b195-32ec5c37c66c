import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataGridComponent } from '../../../../../../../play-comp-library/src/lib/components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-cell-def.directive';
import { AvaTagComponent } from '../../../../../../../play-comp-library/src/lib/components/tags/tags.component';

@Component({
  selector: 'app-basic-usage-demo',
  imports: [
    CommonModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
    AvaTagComponent,
  ],
  templateUrl: './basic-usage-demo.component.html',
  styleUrl: './basic-usage-demo.component.scss',
})
export class BasicUsageDemoComponent {
  basicData = [
    {
      id: 1,
      name: 'Alice Johnson',
      email: '<EMAIL>',
      department: 'Engineering',
      status: 'Active',
    },
    {
      id: 2,
      name: 'Bob Smith',
      email: '<EMAIL>',
      department: 'Marketing',
      status: 'Active',
    },
    {
      id: 3,
      name: 'Carlos Martinez',
      email: '<EMAIL>',
      department: 'Sales',
      status: 'Pending',
    },
    {
      id: 4,
      name: 'Diana Lee',
      email: '<EMAIL>',
      department: 'Engineering',
      status: 'Inactive',
    },
    {
      id: 5,
      name: 'Ethan Brown',
      email: '<EMAIL>',
      department: 'HR',
      status: 'Active',
    },
  ];

  displayedColumns = ['name', 'email', 'department', 'status'];

  /**
   * Get the appropriate color for status tags
   */
  getStatusColor(
    status: string
  ): 'success' | 'warning' | 'error' | 'info' | 'default' {
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'inactive':
        return 'error';
      default:
        return 'default';
    }
  }
}
