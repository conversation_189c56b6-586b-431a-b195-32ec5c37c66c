.demo-content {
}

.demo-section {
  margin-bottom: var(--global-spacing-8, 4rem);

  h2 {
    font-size: var(--global-font-size-2xl, 1.5rem);
    font-weight: var(--global-font-weight-semibold, 600);
    margin-bottom: var(--global-spacing-2, 0.5rem);
    color: var(--color-text-primary);
  }

  p {
    color: var(--color-text-secondary);
    margin-bottom: var(--global-spacing-4, 1.5rem);
    line-height: var(--global-line-height-normal, 1.5);
  }
}

.demo-card {
  max-width: 890px;
  border-radius: var(--global-radius-md, 8px);
  overflow: hidden;
  margin-bottom: var(--global-spacing-6, 3rem);
}

.card-header {
  padding: var(--global-spacing-4, 1.5rem);
  border-bottom: 1px solid var(--color-border-subtle);

  h3 {
    font-size: var(--global-font-size-lg, 1.125rem);
    font-weight: var(--global-font-weight-semibold, 600);
    margin: 0 0 var(--global-spacing-1, 0.25rem) 0;
    color: var(--color-text-primary);
  }

  p {
    font-size: var(--global-font-size-sm, 0.875rem);
    color: var(--color-text-secondary);
    margin: 0;
  }
}

.card-content {
  padding: var(--global-spacing-4, 1.5rem);
}

.status-badge {
  padding: var(--global-spacing-1, 0.25rem) var(--global-spacing-2, 0.5rem);
  border-radius: var(--global-radius-sm, 4px);
  font-size: var(--global-font-size-sm, 0.875rem);
  font-weight: var(--global-font-weight-medium, 500);
  text-transform: uppercase;
  letter-spacing: 0.025em;

  &.status-active {
    background-color: var(--color-success-subtle);
    color: var(--color-success-emphasis);
  }

  &.status-inactive {
    background-color: var(--color-neutral-subtle);
    color: var(--color-neutral-emphasis);
  }

  &.status-pending {
    background-color: var(--color-warning-subtle);
    color: var(--color-warning-emphasis);
  }
}

.sku-code {
  font-family: var(--global-font-family-mono, "Courier New", monospace);
  font-size: var(--global-font-size-sm, 0.875rem);
  background-color: var(--color-background-secondary);
  padding: var(--global-spacing-1, 0.25rem) var(--global-spacing-2, 0.5rem);
  border-radius: var(--global-radius-sm, 4px);
  border: 1px solid var(--color-border-subtle);
}

.category-tag {
  background-color: var(--color-primary-subtle);
  color: var(--color-primary-emphasis);
  padding: var(--global-spacing-1, 0.25rem) var(--global-spacing-2, 0.5rem);
  border-radius: var(--global-radius-sm, 4px);
  font-size: var(--global-font-size-sm, 0.875rem);
  font-weight: var(--global-font-weight-medium, 500);
}

.stock-indicator {
  font-weight: var(--global-font-weight-medium, 500);

  &.low-stock {
    color: var(--color-danger-emphasis);
  }

  &.normal-stock {
    color: var(--color-success-emphasis);
  }
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--global-spacing-4, 1.5rem);
  margin-top: var(--global-spacing-4, 1.5rem);
}

.feature-card {
  padding: var(--global-spacing-4, 1.5rem);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  background-color: var(--color-background-secondary);

  h4 {
    font-size: var(--global-font-size-base, 1rem);
    font-weight: var(--global-font-weight-semibold, 600);
    margin: 0 0 var(--global-spacing-3, 1rem) 0;
    color: var(--color-text-primary);
  }

  ul {
    margin: 0;
    padding-left: var(--global-spacing-4, 1.5rem);

    li {
      font-size: var(--global-font-size-sm, 0.875rem);
      color: var(--color-text-secondary);
      margin-bottom: var(--global-spacing-1, 0.25rem);
      line-height: var(--global-line-height-normal, 1.5);
    }
  }
}

.code-block {
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  padding: var(--global-spacing-4, 1.5rem);
  overflow-x: auto;

  pre {
    margin: 0;
    font-family: var(--global-font-family-mono, "Courier New", monospace);
    font-size: var(--global-font-size-sm, 0.875rem);
    line-height: var(--global-line-height-normal, 1.5);
    color: var(--color-text-primary);
  }

  code {
    white-space: pre;
  }
}
