// =============================================================================
// DATA GRID BASIC USAGE DEMO - CLEAN & REFACTORED STYLES
// =============================================================================

// =============================================================================
// LAYOUT & STRUCTURE
// =============================================================================

.demo-page {
  min-height: 100vh;
  background: var(--color-background-primary);
}

.demo-content {
  padding: 0 0 var(--global-spacing-8, 4rem) 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--global-spacing-4, 1.5rem);
}

.demo-section {
  margin-bottom: var(--global-spacing-10, 5rem);

  &:last-child {
    margin-bottom: 0;
  }
}

// =============================================================================
// TABLE STYLING
// =============================================================================

.styled-data-grid {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
