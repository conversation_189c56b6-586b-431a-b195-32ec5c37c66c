import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataGridComponent } from '../../../../../../../play-comp-library/src/lib/components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../../../../../../play-comp-library/src/lib/components/data-grid/directive/ava-cell-def.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { BadgesComponent } from '../../../../../../../play-comp-library/src/lib/components/badges/badges.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { AvatarsComponent } from '../../../../../../../play-comp-library/src/lib/components/avatars/avatars.component';
import { LinkComponent } from '../../../../../../../play-comp-library/src/lib/components/link/link.component';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/lib/components/toggle/toggle.component';

@Component({
  selector: 'app-custom-cells-demo',
  imports: [
    CommonModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
    ButtonComponent,
    BadgesComponent,
    IconComponent,
    AvatarsComponent,
    LinkComponent,
    ToggleComponent,
  ],
  templateUrl: './custom-cells-demo.component.html',
  styleUrl: './custom-cells-demo.component.scss',
})
export class CustomCellsDemoComponent {
  userData = [
    {
      id: 1,
      name: 'Alice Johnson',
      email: '<EMAIL>',
      avatar: 'AJ',
      role: 'Admin',
      status: 'Active',
      lastLogin: '2024-01-15T10:30:00',
      permissions: 5,
      isActive: true,
    },
    {
      id: 2,
      name: 'Bob Smith',
      email: '<EMAIL>',
      avatar: 'BS',
      role: 'Editor',
      status: 'Inactive',
      lastLogin: '2024-01-10T14:22:00',
      permissions: 3,
      isActive: false,
    },
    {
      id: 3,
      name: 'Carlos Martinez',
      email: '<EMAIL>',
      avatar: 'CM',
      role: 'Viewer',
      status: 'Pending',
      lastLogin: '2024-01-12T09:15:00',
      permissions: 1,
      isActive: true,
    },
    {
      id: 4,
      name: 'Diana Lee',
      email: '<EMAIL>',
      avatar: 'DL',
      role: 'Admin',
      status: 'Active',
      lastLogin: '2024-01-14T16:45:00',
      permissions: 5,
      isActive: true,
    },
    {
      id: 5,
      name: 'Ethan Brown',
      email: '<EMAIL>',
      avatar: 'EB',
      role: 'Editor',
      status: 'Active',
      lastLogin: '2024-01-13T11:20:00',
      permissions: 3,
      isActive: true,
    },
  ];

  userColumns = [
    'user',
    'role',
    'status',
    'lastLogin',
    'permissions',
    'isActive',
    'actions',
  ];

  projectData = [
    {
      id: 1,
      name: 'E-commerce Platform',
      progress: 85,
      priority: 'High',
      team: ['AJ', 'BS', 'CM'],
      deadline: '2024-02-15',
      budget: 150000,
      status: 'In Progress',
    },
    {
      id: 2,
      name: 'Mobile App Redesign',
      progress: 42,
      priority: 'Medium',
      team: ['DL', 'EB'],
      deadline: '2024-03-01',
      budget: 75000,
      status: 'Planning',
    },
    {
      id: 3,
      name: 'Data Analytics Dashboard',
      progress: 95,
      priority: 'High',
      team: ['AJ', 'DL', 'EB', 'CM'],
      deadline: '2024-01-30',
      budget: 200000,
      status: 'Review',
    },
    {
      id: 4,
      name: 'Customer Support Portal',
      progress: 15,
      priority: 'Low',
      team: ['BS', 'CM'],
      deadline: '2024-04-15',
      budget: 50000,
      status: 'Planning',
    },
  ];

  projectColumns = [
    'name',
    'progress',
    'priority',
    'team',
    'deadline',
    'budget',
    'actions',
  ];

  onEdit(item: any) {
    console.log('Edit:', item);
  }

  onDelete(item: any) {
    console.log('Delete:', item);
  }

  onView(item: any) {
    console.log('View:', item);
  }

  onToggleActive(user: any) {
    user.isActive = !user.isActive;
    console.log('Toggle active:', user);
  }

  getRoleBadgeState(
    role: string
  ):
    | 'high-priority'
    | 'medium-priority'
    | 'low-priority'
    | 'neutral'
    | 'information' {
    switch (role) {
      case 'Admin':
        return 'high-priority';
      case 'Editor':
        return 'medium-priority';
      case 'Viewer':
        return 'neutral';
      default:
        return 'neutral';
    }
  }

  getStatusBadgeState(
    status: string
  ):
    | 'high-priority'
    | 'medium-priority'
    | 'low-priority'
    | 'neutral'
    | 'information' {
    switch (status) {
      case 'Active':
        return 'low-priority';
      case 'Inactive':
        return 'neutral';
      case 'Pending':
        return 'medium-priority';
      case 'In Progress':
        return 'information';
      case 'Planning':
        return 'neutral';
      case 'Review':
        return 'medium-priority';
      default:
        return 'neutral';
    }
  }

  getPriorityBadgeState(
    priority: string
  ):
    | 'high-priority'
    | 'medium-priority'
    | 'low-priority'
    | 'neutral'
    | 'information' {
    switch (priority) {
      case 'High':
        return 'high-priority';
      case 'Medium':
        return 'medium-priority';
      case 'Low':
        return 'low-priority';
      default:
        return 'neutral';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }

  isOverdue(deadline: string): boolean {
    return new Date(deadline) < new Date();
  }

  codeExample = `<ava-data-grid [dataSource]="userData" [displayedColumns]="userColumns">
  
  <!-- Avatar + Name cell -->
  <ng-container avaColumnDef="user" [sortable]="true">
    <ng-container *avaHeaderCellDef>User</ng-container>
    <ng-container *avaCellDef="let row">
      <div class="user-cell">
        <ava-avatars [initials]="row.avatar" size="sm"></ava-avatars>
        <div class="user-info">
          <div class="user-name">{{ row.name }}</div>
          <ava-link [href]="'mailto:' + row.email" variant="subtle">
            {{ row.email }}
          </ava-link>
        </div>
      </div>
    </ng-container>
  </ng-container>
  
  <!-- Badge cell -->
  <ng-container avaColumnDef="role" [sortable]="true">
    <ng-container *avaHeaderCellDef>Role</ng-container>
    <ng-container *avaCellDef="let row">
      <ava-badges 
        [label]="row.role" 
        [variant]="getRoleBadgeVariant(row.role)">
      </ava-badges>
    </ng-container>
  </ng-container>
  
  <!-- Action buttons cell -->
  <ng-container avaColumnDef="actions">
    <ng-container *avaHeaderCellDef>Actions</ng-container>
    <ng-container *avaCellDef="let row">
      <div class="action-buttons">
        <ava-button 
          variant="ghost" 
          size="sm" 
          (userClick)="onEdit(row)">
          <ava-icon iconName="SquarePen" iconSize="16"></ava-icon>
        </ava-button>
        <ava-button 
          variant="ghost" 
          size="sm" 
          (userClick)="onDelete(row)">
          <ava-icon iconName="trash" iconSize="16"></ava-icon>
        </ava-button>
      </div>
    </ng-container>
  </ng-container>
  
</ava-data-grid>`;
}
