<div class="demo-page">
  <!-- Header -->
  <div class="demo-header">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <h1>Data Grid</h1>
          <p>
            A comprehensive data table component with advanced features
            including sorting, filtering, custom cell templates, responsive
            design, and flexible column definitions.
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Demo Links -->
  <div class="demo-content">
    <div class="container">
      <div class="demo-grid">
        <div class="demo-card">
          <h3>Basic Usage</h3>
          <p>Simple data grid with basic functionality and clean display.</p>
          <a routerLink="/data-grid-basic-usage" class="demo-link">View Demo →</a>
        </div>

        <div class="demo-card">
          <h3>Sorting</h3>
          <p>Interactive column sorting with multiple sort criteria support.</p>
          <a routerLink="/data-grid-sorting" class="demo-link">View Demo →</a>
        </div>

        <div class="demo-card">
          <h3>Filtering</h3>
          <p>
            Advanced filtering capabilities with search and dropdown filters.
          </p>
          <a routerLink="/data-grid-filtering" class="demo-link">View Demo →</a>
        </div>

        <div class="demo-card">
          <h3>Custom Cells</h3>
          <p>
            Rich content cells with badges, buttons, avatars, and custom
            actions.
          </p>
          <a routerLink="/data-grid-custom-cells" class="demo-link">View Demo →</a>
        </div>

        <div class="demo-card">
          <h3>Responsive Design</h3>
          <p>
            Mobile-friendly tables with horizontal scrolling and adaptive
            layouts.
          </p>
          <a routerLink="/data-grid-responsive" class="demo-link">View Demo →</a>
        </div>

        <div class="demo-card">
          <h3>Advanced Features</h3>
          <p>
            Row selection, inline editing, bulk operations, and complex
            workflows.
          </p>
          <a routerLink="/data-grid-advanced" class="demo-link">View Demo →</a>
        </div>
      </div>
    </div>
  </div>
</div>