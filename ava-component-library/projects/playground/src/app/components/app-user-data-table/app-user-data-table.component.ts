import { Component } from '@angular/core';
import { UserDataTableComponent } from '../../../../../play-comp-library/src/lib/composite-components/user-data-table/user-data-table.component';
import { CommonModule } from '@angular/common';
import { CardComponent } from '../../../../../play-comp-library/src/lib/components/card/card.component';

export interface FieldWithIcon {
  value: string;
  iconName: string;
  clickable: boolean;
}

export interface ActionConfig {
  enabled: boolean;
  label: string;
  icon: string;
  inline: boolean;
}

export interface IconRowData {
  id?: string;
  parentId?: string | null;
  name: FieldWithIcon;
  email: FieldWithIcon;
  access: FieldWithIcon;
  addedOn: FieldWithIcon;
  validtill: FieldWithIcon;
  lastLogin: FieldWithIcon;
  authorized: FieldWithIcon;
  action: Record<string, ActionConfig>;
  sortOrder: number;
  isSelected?: boolean;
}

// For convenience
export type User = IconRowData;
export type RowDataKey = keyof IconRowData;

// Used for defining column display settings
export interface ColumnConfig {
  field: RowDataKey | 'actions' | 'select'; // 'actions' is handled separately in UI
  label: string;
  sortable: boolean;
  filterable: boolean;
  sortingOrder?: number;
  visible: boolean;
  resizable?: boolean;
}

@Component({
  selector: 'ava-user-data-table-demo',
  imports: [CommonModule, UserDataTableComponent, CardComponent],
  templateUrl: './app-user-data-table.component.html',
  styleUrl: './app-user-data-table.component.scss',
})
export class AppUserDataTableComponent {
  columnData: ColumnConfig[] = [
    {
      field: 'select',
      label: '',
      sortable: false,
      filterable: false,
      visible: true,
      resizable: false,
    },
    {
      field: 'name',
      label: 'Name',
      sortable: true,
      filterable: true,
      sortingOrder: 1,
      visible: true,
    },
    {
      field: 'email',
      label: 'Email',
      sortable: true,
      filterable: true,
      sortingOrder: 2,
      visible: true,
    },
    {
      field: 'access',
      label: 'Access',
      sortable: true,
      filterable: true,
      sortingOrder: 3,
      visible: true,
    },
    {
      field: 'addedOn',
      label: 'Added On',
      sortable: false,
      filterable: false,
      sortingOrder: 4,
      visible: true,
    },
    {
      field: 'validtill',
      label: 'Valid Till',
      sortable: true,
      filterable: true,
      sortingOrder: 5,
      visible: true,
    },
    {
      field: 'lastLogin',
      label: 'Last Login',
      sortable: true,
      filterable: false,
      sortingOrder: 6,
      visible: true,
    },
    {
      field: 'authorized',
      label: 'Authorized by',
      sortable: true,
      filterable: false,
      sortingOrder: 7,
      visible: true,
    },
    {
      field: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      sortingOrder: 8,
      visible: true,
    },
  ];

  rowData: User[] = [
    {
      id: '1',
      parentId: '1',
      name: { value: 'Elias Montgomery', iconName: '', clickable: false },
      email: { value: '<EMAIL>', iconName: '', clickable: true },
      access: { value: 'Admin', iconName: '', clickable: false },
      addedOn: { value: '11/02/2023', iconName: '', clickable: false },
      validtill: { value: '12/02/2023', iconName: '', clickable: false },
      lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
      authorized: {
        value: '<EMAIL>',
        iconName: '',
        clickable: false,
      },
      action: {
        edit: {
          enabled: true,
          label: 'Edit',
          icon: 'SquarePen',
          inline: true,
        },
        delete: {
          enabled: true,
          label: 'Delete',
          icon: 'trash',
          inline: true,
        },
      },
      sortOrder: 1,
      isSelected: false,
    },
    {
      id: '2',
      parentId: '2',
      name: {
        value: 'Clara Gontgomery',
        iconName: '',
        clickable: false,
      },
      email: { value: '<EMAIL>', iconName: '', clickable: true },
      access: { value: 'lead', iconName: '', clickable: false },
      addedOn: { value: '12/02/2023', iconName: '', clickable: false },
      validtill: { value: '12/02/2023', iconName: '', clickable: false },
      lastLogin: { value: '12/02/2023', iconName: '', clickable: false },
      authorized: {
        value: '<EMAIL>',
        iconName: '',
        clickable: false,
      },
      action: {
        edit: {
          enabled: true,
          label: 'Edit',
          icon: 'SquarePen',
          inline: false,
        },
        delete: {
          enabled: true,
          label: 'Delete',
          icon: 'trash',
          inline: false,
        },
      },
      sortOrder: 2,
      isSelected: false,
    },
    {
      id: '3',
      parentId: '3',
      name: { value: 'Alias', iconName: '', clickable: false },
      email: { value: '<EMAIL>', iconName: '', clickable: true },
      access: { value: 'Admin', iconName: '', clickable: false },
      addedOn: { value: '11/02/2023', iconName: '', clickable: false },
      validtill: { value: '12/02/2023', iconName: '', clickable: false },
      lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
      authorized: {
        value: '<EMAIL>',
        iconName: '',
        clickable: false,
      },
      action: {
        edit: {
          enabled: true,
          label: 'Edit',
          icon: 'SquarePen',
          inline: true,
        },
        delete: {
          enabled: true,
          label: 'Delete',
          icon: 'trash',
          inline: true,
        },
      },
      sortOrder: 3,
      isSelected: false,
    },
    {
      id: '4',
      parentId: '4',
      name: { value: 'Montgomery', iconName: '', clickable: false },
      email: { value: '<EMAIL>', iconName: '', clickable: true },
      access: { value: 'Admin', iconName: '', clickable: false },
      addedOn: { value: '11/02/2023', iconName: '', clickable: false },
      validtill: { value: '12/02/2023', iconName: '', clickable: false },
      lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
      authorized: {
        value: '<EMAIL>',
        iconName: '',
        clickable: false,
      },
      action: {
        edit: {
          enabled: true,
          label: 'Edit',
          icon: 'SquarePen',
          inline: true,
        },
        delete: {
          enabled: true,
          label: 'Delete',
          icon: 'trash',
          inline: true,
        },
      },
      sortOrder: 4,
      isSelected: false,
    },
    {
      id: '5',
      parentId: '5',
      name: { value: 'Mias Montgomer', iconName: '', clickable: false },
      email: { value: '<EMAIL>', iconName: '', clickable: true },
      access: { value: 'Admin1', iconName: '', clickable: false },
      addedOn: { value: '11/02/2023', iconName: '', clickable: false },
      validtill: { value: '12/02/2023', iconName: '', clickable: false },
      lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
      authorized: {
        value: '<EMAIL>',
        iconName: '',
        clickable: false,
      },
      action: {
        edit: {
          enabled: true,
          label: 'Edit',
          icon: 'SquarePen',
          inline: true,
        },
        delete: {
          enabled: true,
          label: 'Delete',
          icon: 'trash',
          inline: true,
        },
      },
      sortOrder: 5,
      isSelected: false,
    },
    {
      id: '5',
      parentId: '5',
      name: { value: 'lias Montgomer', iconName: '', clickable: false },
      email: { value: '<EMAIL>', iconName: '', clickable: true },
      access: { value: 'Admin1', iconName: '', clickable: false },
      addedOn: { value: '11/02/2023', iconName: '', clickable: false },
      validtill: { value: '12/02/2023', iconName: '', clickable: false },
      lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
      authorized: {
        value: '<EMAIL>',
        iconName: '',
        clickable: false,
      },
      action: {
        edit: {
          enabled: true,
          label: 'Edit',
          icon: 'SquarePen',
          inline: true,
        },
        delete: {
          enabled: true,
          label: 'Delete',
          icon: 'trash',
          inline: true,
        },
      },
      sortOrder: 5,
      isSelected: false,
    },
    {
      id: '5',
      parentId: '5',
      name: { value: 'lias Montgomer', iconName: '', clickable: false },
      email: { value: '<EMAIL>', iconName: '', clickable: true },
      access: { value: 'Admin1', iconName: '', clickable: false },
      addedOn: { value: '11/02/2023', iconName: '', clickable: false },
      validtill: { value: '12/02/2023', iconName: '', clickable: false },
      lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
      authorized: {
        value: '<EMAIL>',
        iconName: '',
        clickable: false,
      },
      action: {
        edit: {
          enabled: true,
          label: 'Edit',
          icon: 'SquarePen',
          inline: true,
        },
        delete: {
          enabled: true,
          label: 'Delete',
          icon: 'trash',
          inline: true,
        },
      },
      sortOrder: 5,
      isSelected: false,
    },
    {
      id: '5',
      parentId: '5',
      name: { value: 'lias Montgomer', iconName: '', clickable: false },
      email: { value: '<EMAIL>', iconName: '', clickable: true },
      access: { value: 'Admin1', iconName: '', clickable: false },
      addedOn: { value: '11/02/2023', iconName: '', clickable: false },
      validtill: { value: '12/02/2023', iconName: '', clickable: false },
      lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
      authorized: {
        value: '<EMAIL>',
        iconName: '',
        clickable: false,
      },
      action: {
        edit: {
          enabled: true,
          label: 'Edit',
          icon: 'SquarePen',
          inline: true,
        },
        delete: {
          enabled: true,
          label: 'Delete',
          icon: 'trash',
          inline: true,
        },
      },
      sortOrder: 5,
      isSelected: false,
    },
  ];

  currentPage = 1;
  pageSize = 6;

  get totalPages(): number {
    return Math.ceil(this.rowData.length / this.pageSize);
  }

  get paginatedData(): User[] {
    const start = (this.currentPage - 1) * this.pageSize;
    const end = start + this.pageSize;
    return this.rowData.slice(start, end);
  }

  onPageChange(page: number) {
    console.log('Page changed:', page);
    this.currentPage = page;
  }

  onTableCellClick(event: { row: User; field: string }) {
    console.log('Clicked cell data:', event);
  }

  onActionFromTable(event: {
    row: User;
    actionKey: string;
    config: ActionConfig;
  }): void {
    console.log('Action clicked:', event.actionKey);
    console.log('Label:', event.config.label);
    console.log('Row:', event.row);

    if (event.actionKey === 'edit') {
      // handle edit
    } else if (event.actionKey === 'delete') {
      // handle delete
    }
  }

  handleColumnOrderChange(updatedColumns: ColumnConfig[]) {
    console.log('Updated column order:', updatedColumns);
  }

  handleRowOrderChange(updatedData: User[]) {
    console.log('Updated row order:', updatedData);
  }

  onSelectedRowsChanged(selectedRows: any[]): void {
    console.log('Selected rows:', selectedRows);
  }
}
