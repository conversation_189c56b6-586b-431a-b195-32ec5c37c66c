.journal-data-grid-demo {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  font-family: var(--grid-font-family-body, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);

  /* Custom styling for demo cells */
  .journal-id-link {
    color: var(--color-brand-primary, #007bff);
    cursor: pointer;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;

    &:hover,
    &:focus {
      color: var(--color-brand-primary-hover, var(--color-brand-primary, #0056b3));
      text-decoration: underline;
    }

    &:focus {
      outline: 2px solid var(--color-brand-primary, #007bff);
      outline-offset: 2px;
      border-radius: 2px;
    }
  }

  .journal-description {
    display: block;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .totals-amount {
    font-weight: 500;
    font-variant-numeric: tabular-nums;
    text-align: right;
    display: block;
  }

  .documents-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 24px;

    .document-icon {
      transition: color 0.2s ease, transform 0.2s ease;

      &:hover,
      &:focus {
        color: var(--color-brand-primary, #007bff) !important;
        transform: scale(1.1);
      }

      &:focus {
        outline: 2px solid var(--color-brand-primary, #007bff);
        outline-offset: 2px;
        border-radius: 2px;
      }
    }

    .no-documents {
      color: var(--grid-text-disabled, #6c757d);
      font-size: 14px;
    }
  }

  .demo-header {
    margin-bottom: 3rem;
    text-align: center;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin-bottom: 1rem;
    }

    .demo-description {
      font-size: 1.125rem;
      color: var(--color-text-secondary);
      max-width: 800px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  .demo-controls {
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: var(--color-background-secondary);
    border-radius: 12px;
    border: 1px solid var(--color-border-subtle);

    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 1rem;
    }

    .control-buttons {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;

      .demo-btn {
        padding: 0.5rem 1rem;
        background: var(--color-brand-primary);
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background: var(--color-brand-primary-hover, var(--color-brand-primary));
        }

        &:focus {
          outline: 2px solid var(--color-brand-primary);
          outline-offset: 2px;
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 4rem;

    h2 {
      font-size: 1.875rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 1rem;
    }

    > p {
      font-size: 1rem;
      color: var(--color-text-secondary);
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .demo-card {
      background: var(--color-background-primary);
      border: 1px solid var(--color-border-subtle);
      border-radius: 12px;
      overflow: hidden;
      margin-bottom: 2rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .card-header {
        padding: 1.5rem;
        background: var(--color-background-secondary);
        border-bottom: 1px solid var(--color-border-subtle);

        h3 {
          font-size: 1.25rem;
          font-weight: 600;
          color: var(--color-text-primary);
          margin-bottom: 0.5rem;
        }

        p {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
          margin: 0;
        }
      }

      .card-content {
        padding: 1.5rem;
      }
    }

    .code-example {
      background: var(--color-background-secondary);
      border: 1px solid var(--color-border-subtle);
      border-radius: 8px;
      overflow: hidden;

      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--color-text-primary);
        margin: 0;
        padding: 1rem 1.5rem;
        background: var(--color-background-tertiary, var(--color-background-secondary));
        border-bottom: 1px solid var(--color-border-subtle);
      }

      pre {
        margin: 0;
        padding: 1.5rem;
        overflow-x: auto;
        background: var(--color-background-primary);

        code {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.875rem;
          line-height: 1.5;
          color: var(--color-text-primary);
          white-space: pre;
        }
      }
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;

      .feature-card {
        padding: 1.5rem;
        background: var(--color-background-primary);
        border: 1px solid var(--color-border-subtle);
        border-radius: 8px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        h4 {
          font-size: 1rem;
          font-weight: 600;
          color: var(--color-text-primary);
          margin-bottom: 0.5rem;
        }

        p {
          font-size: 0.875rem;
          color: var(--color-text-secondary);
          margin: 0;
          line-height: 1.5;
        }
      }
    }

    .events-list {
      background: var(--color-background-secondary);
      border: 1px solid var(--color-border-subtle);
      border-radius: 8px;
      padding: 1.5rem;

      .event-item {
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--color-border-subtle);
        font-size: 0.875rem;
        color: var(--color-text-secondary);

        &:last-child {
          border-bottom: none;
        }

        strong {
          color: var(--color-text-primary);
          font-weight: 600;
        }
      }
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    padding: 1rem;

    .demo-header {
      margin-bottom: 2rem;

      h1 {
        font-size: 2rem;
      }

      .demo-description {
        font-size: 1rem;
      }
    }

    .demo-controls {
      margin-bottom: 2rem;

      .control-buttons {
        flex-direction: column;

        .demo-btn {
          width: 100%;
        }
      }
    }

    .demo-section {
      margin-bottom: 3rem;

      h2 {
        font-size: 1.5rem;
      }

      .demo-card {
        .card-header,
        .card-content {
          padding: 1rem;
        }
      }

      .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .feature-card {
          padding: 1rem;
        }
      }
    }
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .demo-card {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .features-grid .feature-card:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .demo-card,
    .code-example,
    .features-grid .feature-card,
    .events-list {
      border-width: 2px;
    }

    .demo-btn {
      border: 2px solid var(--color-brand-primary);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .features-grid .feature-card {
      transition: none;

      &:hover {
        transform: none;
      }
    }

    .demo-btn {
      transition: none;
    }
  }
}
