import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { 
  JournalDataGridComponent, 
  JournalEntry, 
  JournalDataGridConfig,
  JournalDataGridEvent 
} from 'play-comp-library';

@Component({
  selector: 'app-journal-data-grid',
  standalone: true,
  imports: [CommonModule, JournalDataGridComponent],
  templateUrl: './app-journal-data-grid.component.html',
  styleUrl: './app-journal-data-grid.component.scss'
})
export class AppJournalDataGridComponent {
  
  // Sample journal data matching the image provided
  journalData: JournalEntry[] = [
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: false
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: false
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    }
  ];

  // Configuration for basic demo
  basicConfig: JournalDataGridConfig = {
    showSearch: true,
    showCreateButton: true,
    showFilter: true,
    showSort: true,
    zebraLines: false,
    searchPlaceholder: 'Search for Journal Entries',
    createButtonLabel: 'Create Journal Entry',
    emptyMessage: 'No journal entries found',
    loadingMessage: 'Loading journal entries...'
  };

  // Configuration for zebra lines demo
  zebraConfig: JournalDataGridConfig = {
    ...this.basicConfig,
    zebraLines: true
  };

  // Configuration for minimal demo (no search, no create button)
  minimalConfig: JournalDataGridConfig = {
    showSearch: false,
    showCreateButton: false,
    showFilter: false,
    showSort: true,
    zebraLines: false,
    customColumns: ['journalId', 'journalDescription', 'journalStatus', 'documents']
  };

  // Configuration for custom columns demo
  customColumnsConfig: JournalDataGridConfig = {
    ...this.basicConfig,
    customColumns: ['journalId', 'date', 'journalStatus', 'drCrTotals', 'documents']
  };

  // Loading state demo
  isLoading = false;

  // Event handlers
  onJournalEvent(event: JournalDataGridEvent) {
    console.log('Journal Event:', event);
  }

  onCreateJournalEntry() {
    console.log('Create Journal Entry clicked');
    // In a real app, this would open a dialog or navigate to a form
  }

  onRowClick(entry: JournalEntry) {
    console.log('Row clicked:', entry);
    // In a real app, this would navigate to journal details
  }

  onDocumentClick(entry: JournalEntry) {
    console.log('Document clicked for:', entry.journalId);
    // In a real app, this would open document viewer
  }

  onSearchChange(searchTerm: string) {
    console.log('Search term:', searchTerm);
    // In a real app, this would trigger API call for filtered data
  }

  // Demo actions
  toggleLoading() {
    this.isLoading = !this.isLoading;
  }

  addRandomEntry() {
    const statuses: Array<JournalEntry['journalStatus']> = ['Template', 'Posted', 'Rejected', 'Ready to Approve', 'Draft'];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    const newEntry: JournalEntry = {
      journalId: `${Math.floor(Math.random() * 900000) + 100000}`,
      date: new Date().toLocaleDateString(),
      journalDescription: `New journal entry ${Date.now()}`,
      journalStatus: randomStatus,
      sourceTransaction: 'Manual',
      drCrTotals: Math.random() > 0.5 ? `$ ${Math.floor(Math.random() * 100000)}` : 'N/A',
      documents: Math.random() > 0.5
    };
    this.journalData = [...this.journalData, newEntry];
  }

  clearData() {
    this.journalData = [];
  }

  resetData() {
    // Reset to original sample data
    this.journalData = [
      {
        journalId: '388236',
        date: 'mm/dd/yyyy',
        journalDescription: 'This is a template for mortgage payments',
        journalStatus: 'Template',
        sourceTransaction: 'Manual',
        drCrTotals: 'N/A',
        documents: true
      },
      {
        journalId: '388237',
        date: 'mm/dd/yyyy',
        journalDescription: 'Posted journal entry',
        journalStatus: 'Posted',
        sourceTransaction: 'Manual',
        drCrTotals: '$ 204893',
        documents: false
      },
      {
        journalId: '388238',
        date: 'mm/dd/yyyy',
        journalDescription: 'Rejected entry needs review',
        journalStatus: 'Rejected',
        sourceTransaction: 'Manual',
        drCrTotals: 'N/A',
        documents: true
      },
      {
        journalId: '388239',
        date: 'mm/dd/yyyy',
        journalDescription: 'Ready for approval',
        journalStatus: 'Ready to Approve',
        sourceTransaction: 'Manual',
        drCrTotals: '$ 150000',
        documents: true
      },
      {
        journalId: '388240',
        date: 'mm/dd/yyyy',
        journalDescription: 'Draft entry in progress',
        journalStatus: 'Draft',
        sourceTransaction: 'Manual',
        drCrTotals: '$ 75000',
        documents: false
      }
    ];
  }

  // Code examples for documentation
  basicUsageCode = `<ava-journal-data-grid
  [data]="journalData"
  [config]="basicConfig"
  (createClick)="onCreateJournalEntry()"
  (rowClick)="onRowClick($event)"
  (documentClick)="onDocumentClick($event)"
  (searchChange)="onSearchChange($event)"
  (journalEvent)="onJournalEvent($event)">
</ava-journal-data-grid>`;

  configurationCode = `const config: JournalDataGridConfig = {
  showSearch: true,
  showCreateButton: true,
  showFilter: true,
  showSort: true,
  zebraLines: false,
  searchPlaceholder: 'Search for Journal Entries',
  createButtonLabel: 'Create Journal Entry',
  customColumns: ['journalId', 'date', 'journalStatus', 'documents']
};`;

  dataStructureCode = `interface JournalEntry {
  journalId: string;
  date: string;
  journalDescription: string;
  journalStatus: 'Template' | 'Posted' | 'Rejected' | 'Ready to Approve' | 'Draft';
  sourceTransaction: string;
  drCrTotals: string;
  documents?: boolean;
  [key: string]: any; // Allow additional properties
}`;
}
