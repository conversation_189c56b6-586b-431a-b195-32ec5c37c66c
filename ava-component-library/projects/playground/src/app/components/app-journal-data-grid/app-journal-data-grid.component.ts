import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  JournalDataGridComponent,
  JournalDataGridConfig,
  JournalDataGridEvent,
  AvaColumnDefDirective,
  AvaHeaderCellDefDirective,
  AvaCellDefDirective,
  AvaTagComponent,
  IconComponent
} from 'play-comp-library';

// Define your own data interface
interface JournalEntry {
  journalId: string;
  date: string;
  journalDescription: string;
  journalStatus: 'Template' | 'Posted' | 'Rejected' | 'Ready to Approve' | 'Draft';
  sourceTransaction: string;
  drCrTotals: string;
  documents?: boolean;
}

@Component({
  selector: 'app-journal-data-grid',
  standalone: true,
  imports: [
    CommonModule,
    JournalDataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
    AvaTagComponent,
    IconComponent
  ],
  templateUrl: './app-journal-data-grid.component.html',
  styleUrl: './app-journal-data-grid.component.scss'
})
export class AppJournalDataGridComponent {
  
  // Sample journal data matching the image provided
  journalData: JournalEntry[] = [
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: false
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: false
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '$ 204893',
      documents: true
    }
  ];

  // Configuration for basic demo
  basicConfig: JournalDataGridConfig = {
    showSearch: true,
    showCreateButton: true,
    zebraLines: false,
    searchPlaceholder: 'Search for Journal Entries',
    createButtonLabel: 'Create Journal Entry',
    emptyMessage: 'No journal entries found',
    loadingMessage: 'Loading journal entries...'
  };

  // Configuration for zebra lines demo
  zebraConfig: JournalDataGridConfig = {
    ...this.basicConfig,
    zebraLines: true
  };

  // Configuration for minimal demo (no search, no create button)
  minimalConfig: JournalDataGridConfig = {
    showSearch: false,
    showCreateButton: false,
    zebraLines: false
  };

  // Configuration for custom columns demo
  customColumnsConfig: JournalDataGridConfig = {
    ...this.basicConfig
  };

  // Displayed columns for the data grid
  displayedColumns = ['journalId', 'date', 'journalDescription', 'journalStatus', 'sourceTransaction', 'drCrTotals', 'documents'];

  // Loading state demo
  isLoading = false;

  // Event handlers
  onJournalEvent(event: JournalDataGridEvent) {
    console.log('Journal Event:', event);
  }

  onCreateJournalEntry() {
    console.log('Create Journal Entry clicked');
    // In a real app, this would open a dialog or navigate to a form
  }

  onRowClick(entry: JournalEntry) {
    console.log('Row clicked:', entry);
    // In a real app, this would navigate to journal details
  }

  onDocumentClick(entry: JournalEntry, event: Event) {
    event.stopPropagation(); // Prevent row click
    console.log('Document clicked for:', entry.journalId);
    // In a real app, this would open document viewer
  }

  onSearchChange(searchTerm: string) {
    console.log('Search term:', searchTerm);
    // In a real app, this would trigger API call for filtered data
  }

  onDataSorted(sortedData: any[]) {
    console.log('Data sorted:', sortedData);
    // In a real app, this would update the data source
  }

  // Helper method for status tag colors
  getStatusTagColor(status: string): 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' {
    switch (status) {
      case 'Posted':
        return 'success';
      case 'Template':
        return 'info';
      case 'Rejected':
        return 'error';
      case 'Ready to Approve':
        return 'warning';
      case 'Draft':
        return 'default';
      default:
        return 'default';
    }
  }

  // Demo actions
  toggleLoading() {
    this.isLoading = !this.isLoading;
  }

  addRandomEntry() {
    const statuses: Array<JournalEntry['journalStatus']> = ['Template', 'Posted', 'Rejected', 'Ready to Approve', 'Draft'];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    const newEntry: JournalEntry = {
      journalId: `${Math.floor(Math.random() * 900000) + 100000}`,
      date: new Date().toLocaleDateString(),
      journalDescription: `New journal entry ${Date.now()}`,
      journalStatus: randomStatus,
      sourceTransaction: 'Manual',
      drCrTotals: Math.random() > 0.5 ? `$ ${Math.floor(Math.random() * 100000)}` : 'N/A',
      documents: Math.random() > 0.5
    };
    this.journalData = [...this.journalData, newEntry];
  }

  clearData() {
    this.journalData = [];
  }

  resetData() {
    // Reset to original sample data
    this.journalData = [
      {
        journalId: '388236',
        date: 'mm/dd/yyyy',
        journalDescription: 'This is a template for mortgage payments',
        journalStatus: 'Template',
        sourceTransaction: 'Manual',
        drCrTotals: 'N/A',
        documents: true
      },
      {
        journalId: '388237',
        date: 'mm/dd/yyyy',
        journalDescription: 'Posted journal entry',
        journalStatus: 'Posted',
        sourceTransaction: 'Manual',
        drCrTotals: '$ 204893',
        documents: false
      },
      {
        journalId: '388238',
        date: 'mm/dd/yyyy',
        journalDescription: 'Rejected entry needs review',
        journalStatus: 'Rejected',
        sourceTransaction: 'Manual',
        drCrTotals: 'N/A',
        documents: true
      },
      {
        journalId: '388239',
        date: 'mm/dd/yyyy',
        journalDescription: 'Ready for approval',
        journalStatus: 'Ready to Approve',
        sourceTransaction: 'Manual',
        drCrTotals: '$ 150000',
        documents: true
      },
      {
        journalId: '388240',
        date: 'mm/dd/yyyy',
        journalDescription: 'Draft entry in progress',
        journalStatus: 'Draft',
        sourceTransaction: 'Manual',
        drCrTotals: '$ 75000',
        documents: false
      }
    ];
  }

  // Code examples for documentation
  basicUsageCode = `<ava-journal-data-grid
  [dataSource]="journalData"
  [displayedColumns]="displayedColumns"
  [config]="basicConfig"
  (createClick)="onCreateJournalEntry()"
  (searchChange)="onSearchChange($event)"
  (journalEvent)="onJournalEvent($event)">

  <!-- Define your own columns -->
  <ng-container avaColumnDef="journalId" [sortable]="true">
    <ng-container *avaHeaderCellDef>Journal ID</ng-container>
    <ng-container *avaCellDef="let row">{{ row.journalId }}</ng-container>
  </ng-container>

  <ng-container avaColumnDef="journalStatus">
    <ng-container *avaHeaderCellDef>Status</ng-container>
    <ng-container *avaCellDef="let row">
      <ava-tag [label]="row.journalStatus" variant="outlined"></ava-tag>
    </ng-container>
  </ng-container>

  <!-- Add more columns as needed -->
</ava-journal-data-grid>`;

  configurationCode = `const config: JournalDataGridConfig = {
  showSearch: true,
  showCreateButton: true,
  zebraLines: false,
  searchPlaceholder: 'Search entries',
  createButtonLabel: 'Create Entry'
};

// Define your own data interface
interface JournalEntry {
  journalId: string;
  date: string;
  journalDescription: string;
  journalStatus: string;
  // ... any other properties
}`;

  dataStructureCode = `// The component is now flexible - you define your own data structure
interface YourDataType {
  id: string;
  name: string;
  status: string;
  // ... any properties you need
}

// Then use it with your own column definitions
<ava-journal-data-grid [dataSource]="yourData" [displayedColumns]="yourColumns">
  <ng-container avaColumnDef="id">
    <ng-container *avaHeaderCellDef>ID</ng-container>
    <ng-container *avaCellDef="let row">{{ row.id }}</ng-container>
  </ng-container>
  <!-- Define more columns as needed -->
</ava-journal-data-grid>`;
}
