<div class="journal-data-grid-demo">
  <div class="demo-header">
    <h1>Journal Data Grid</h1>
    <p class="demo-description">
      A specialized composite component built on top of the data-grid component, 
      designed specifically for journal entry management with status tags and document icons.
    </p>
  </div>

  <!-- Demo Controls -->
  <div class="demo-controls">
    <h2>Demo Controls</h2>
    <div class="control-buttons">
      <button class="demo-btn" (click)="toggleLoading()">
        {{ isLoading ? 'Stop Loading' : 'Show Loading' }}
      </button>
      <button class="demo-btn" (click)="addRandomEntry()">Add Random Entry</button>
      <button class="demo-btn" (click)="clearData()">Clear Data</button>
      <button class="demo-btn" (click)="resetData()">Reset Data</button>
    </div>
  </div>

  <!-- Basic Usage Demo -->
  <div class="demo-section">
    <h2>Basic Usage</h2>
    <p>
      Complete journal data grid with search, create button, and all features enabled.
      Status tags use outlined variants with appropriate colors, and document icons are clickable.
    </p>
    
    <div class="demo-card">
      <div class="card-header">
        <h3>Journal Entries Management</h3>
        <p>Full-featured journal data grid with all controls</p>
      </div>
      <div class="card-content">
        <ava-journal-data-grid
          [data]="journalData"
          [config]="basicConfig"
          [loading]="isLoading"
          (createClick)="onCreateJournalEntry()"
          (rowClick)="onRowClick($event)"
          (documentClick)="onDocumentClick($event)"
          (searchChange)="onSearchChange($event)"
          (journalEvent)="onJournalEvent($event)">
        </ava-journal-data-grid>
      </div>
    </div>

    <div class="code-example">
      <h4>HTML</h4>
      <pre><code>{{ basicUsageCode }}</code></pre>
    </div>
  </div>

  <!-- Zebra Lines Demo -->
  <div class="demo-section">
    <h2>With Zebra Lines</h2>
    <p>
      Same functionality with alternating row colors for better readability.
    </p>
    
    <div class="demo-card">
      <div class="card-header">
        <h3>Journal Entries with Zebra Lines</h3>
        <p>Enhanced readability with alternating row colors</p>
      </div>
      <div class="card-content">
        <ava-journal-data-grid
          [data]="journalData"
          [config]="zebraConfig"
          [loading]="isLoading"
          (createClick)="onCreateJournalEntry()"
          (rowClick)="onRowClick($event)"
          (documentClick)="onDocumentClick($event)"
          (searchChange)="onSearchChange($event)"
          (journalEvent)="onJournalEvent($event)">
        </ava-journal-data-grid>
      </div>
    </div>
  </div>

  <!-- Minimal Demo -->
  <div class="demo-section">
    <h2>Minimal Configuration</h2>
    <p>
      Simplified version with no search or create button, showing only essential columns.
      Perfect for read-only displays or embedded views.
    </p>
    
    <div class="demo-card">
      <div class="card-header">
        <h3>Read-Only Journal View</h3>
        <p>Minimal configuration for display purposes</p>
      </div>
      <div class="card-content">
        <ava-journal-data-grid
          [data]="journalData"
          [config]="minimalConfig"
          [loading]="isLoading"
          (rowClick)="onRowClick($event)"
          (documentClick)="onDocumentClick($event)"
          (journalEvent)="onJournalEvent($event)">
        </ava-journal-data-grid>
      </div>
    </div>
  </div>

  <!-- Custom Columns Demo -->
  <div class="demo-section">
    <h2>Custom Columns</h2>
    <p>
      Customized column selection showing only specific fields relevant to your use case.
    </p>
    
    <div class="demo-card">
      <div class="card-header">
        <h3>Custom Column Selection</h3>
        <p>Show only the columns you need</p>
      </div>
      <div class="card-content">
        <ava-journal-data-grid
          [data]="journalData"
          [config]="customColumnsConfig"
          [loading]="isLoading"
          (createClick)="onCreateJournalEntry()"
          (rowClick)="onRowClick($event)"
          (documentClick)="onDocumentClick($event)"
          (searchChange)="onSearchChange($event)"
          (journalEvent)="onJournalEvent($event)">
        </ava-journal-data-grid>
      </div>
    </div>
  </div>

  <!-- Configuration Documentation -->
  <div class="demo-section">
    <h2>Configuration</h2>
    <p>
      The journal data grid accepts a configuration object to customize its behavior and appearance.
    </p>
    
    <div class="code-example">
      <h4>Configuration Interface</h4>
      <pre><code>{{ configurationCode }}</code></pre>
    </div>
  </div>

  <!-- Data Structure Documentation -->
  <div class="demo-section">
    <h2>Data Structure</h2>
    <p>
      The component expects an array of JournalEntry objects with the following structure:
    </p>
    
    <div class="code-example">
      <h4>JournalEntry Interface</h4>
      <pre><code>{{ dataStructureCode }}</code></pre>
    </div>
  </div>

  <!-- Features List -->
  <div class="demo-section">
    <h2>Features</h2>
    <div class="features-grid">
      <div class="feature-card">
        <h4>🏷️ Status Tags</h4>
        <p>Outlined status tags with color-coded states for easy visual identification</p>
      </div>
      <div class="feature-card">
        <h4>📄 Document Icons</h4>
        <p>Clickable document icons using Lucide Angular icons for file access</p>
      </div>
      <div class="feature-card">
        <h4>🔍 Search & Filter</h4>
        <p>Built-in search functionality with configurable placeholder text</p>
      </div>
      <div class="feature-card">
        <h4>➕ Create Button</h4>
        <p>Integrated create button with customizable label and click handling</p>
      </div>
      <div class="feature-card">
        <h4>📊 Flexible Columns</h4>
        <p>Configurable column selection to show only relevant data</p>
      </div>
      <div class="feature-card">
        <h4>🎨 Zebra Lines</h4>
        <p>Optional alternating row colors for improved readability</p>
      </div>
      <div class="feature-card">
        <h4>♿ Accessibility</h4>
        <p>Full keyboard navigation and screen reader support</p>
      </div>
      <div class="feature-card">
        <h4>📱 Responsive</h4>
        <p>Mobile-friendly design with horizontal scrolling for large tables</p>
      </div>
    </div>
  </div>

  <!-- Events Documentation -->
  <div class="demo-section">
    <h2>Events</h2>
    <p>
      The component emits various events for interaction handling:
    </p>
    
    <div class="events-list">
      <div class="event-item">
        <strong>createClick</strong> - Emitted when the create button is clicked
      </div>
      <div class="event-item">
        <strong>rowClick</strong> - Emitted when a journal ID is clicked
      </div>
      <div class="event-item">
        <strong>documentClick</strong> - Emitted when a document icon is clicked
      </div>
      <div class="event-item">
        <strong>searchChange</strong> - Emitted when the search term changes
      </div>
      <div class="event-item">
        <strong>journalEvent</strong> - Unified event emitter for all interactions
      </div>
    </div>
  </div>
</div>
