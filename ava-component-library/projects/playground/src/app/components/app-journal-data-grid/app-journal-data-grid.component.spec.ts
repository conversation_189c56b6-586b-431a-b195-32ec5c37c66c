import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AppJournalDataGridComponent } from './app-journal-data-grid.component';

describe('AppJournalDataGridComponent', () => {
  let component: AppJournalDataGridComponent;
  let fixture: ComponentFixture<AppJournalDataGridComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AppJournalDataGridComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AppJournalDataGridComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have initial journal data', () => {
    expect(component.journalData.length).toBeGreaterThan(0);
  });

  it('should toggle loading state', () => {
    const initialState = component.isLoading;
    component.toggleLoading();
    expect(component.isLoading).toBe(!initialState);
  });

  it('should add random entry', () => {
    const initialLength = component.journalData.length;
    component.addRandomEntry();
    expect(component.journalData.length).toBe(initialLength + 1);
  });

  it('should clear data', () => {
    component.clearData();
    expect(component.journalData.length).toBe(0);
  });

  it('should reset data', () => {
    component.clearData();
    component.resetData();
    expect(component.journalData.length).toBeGreaterThan(0);
  });
});
