.ordering-demo {
  padding: 2rem;
  background-color: var(--surface-background);
  max-width: 880px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 3rem;
  text-align: center;

  .back-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: 1rem;
    transition: color 0.3s ease;

    &:hover {
      color: var(--text-primary);
    }
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
  }

  p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
  }
}

.demo-order-container {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  gap: 1rem;
}

.demo-order-item {
  background-color: #3b82f6;
  color: white;
  padding: 1rem;
  border-radius: 4px;
  flex: 1;
  text-align: center;
  font-weight: 500;
}
