<div class="flexbox-demo">
  <div class="demo-content">
    <ava-grid-demo-card
      title="Flex Direction"
      description="Control the direction of flex items with .flex-row, .flex-row-reverse, .flex-column, and .flex-column-reverse."
    >
      <div class="d-flex flex-row demo-flex-container">
        <div class="demo-flex-item">1</div>
        <div class="demo-flex-item">2</div>
        <div class="demo-flex-item">3</div>
      </div>
      <div class="d-flex flex-row-reverse demo-flex-container">
        <div class="demo-flex-item">1</div>
        <div class="demo-flex-item">2</div>
        <div class="demo-flex-item">3</div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Justify Content"
      description="Align flex items along the main axis with .justify-content-..."
    >
      <div class="d-flex justify-content-start demo-flex-container">
        <div class="demo-flex-item">Item</div>
        <div class="demo-flex-item">Item</div>
      </div>
      <div class="d-flex justify-content-center demo-flex-container">
        <div class="demo-flex-item">Item</div>
        <div class="demo-flex-item">Item</div>
      </div>
      <div class="d-flex justify-content-end demo-flex-container">
        <div class="demo-flex-item">Item</div>
        <div class="demo-flex-item">Item</div>
      </div>
      <div class="d-flex justify-content-between demo-flex-container">
        <div class="demo-flex-item">Item</div>
        <div class="demo-flex-item">Item</div>
      </div>
      <div class="d-flex justify-content-around demo-flex-container">
        <div class="demo-flex-item">Item</div>
        <div class="demo-flex-item">Item</div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Align Items"
      description="Align flex items along the cross axis with .align-items-..."
    >
      <div
        class="d-flex align-items-start demo-flex-container"
        style="height: 100px"
      >
        <div class="demo-flex-item">Item</div>
        <div class="demo-flex-item">Item</div>
      </div>
      <div
        class="d-flex align-items-center demo-flex-container"
        style="height: 100px"
      >
        <div class="demo-flex-item">Item</div>
        <div class="demo-flex-item">Item</div>
      </div>
      <div
        class="d-flex align-items-end demo-flex-container"
        style="height: 100px"
      >
        <div class="demo-flex-item">Item</div>
        <div class="demo-flex-item">Item</div>
      </div>
    </ava-grid-demo-card>
  </div>
</div>
