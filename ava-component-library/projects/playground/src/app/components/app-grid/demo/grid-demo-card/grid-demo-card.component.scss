.demo-card {
  background-color: var(--surface-default);
  border: 1px solid var(--border-subtle);
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-subtle);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.card-description {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

.card-content {
  padding: 1.5rem;
}
