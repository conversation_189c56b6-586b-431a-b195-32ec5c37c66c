<div class="basic-usage-demo">
  <div class="demo-content">
    <ava-grid-demo-card
      title="Container Types"
      description="Choose between a fixed-width or full-width container."
    >
      <div class="row">
        <div class="col-6">
          <div class="demo-container container">
            <div class="demo-content-box">.container</div>
          </div>
        </div>
        <div class="col-6">
          <div class="demo-container container-fluid">
            <div class="demo-content-box">.container-fluid</div>
          </div>
        </div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Basic Column System"
      description="The grid is divided into 12 columns. Use classes like .col-6 for a 6-column width."
    >
      <div class="row">
        <div class="col-12 demo-col">col-12</div>
      </div>
      <div class="row">
        <div class="col-6 demo-col">col-6</div>
        <div class="col-6 demo-col">col-6</div>
      </div>
      <div class="row">
        <div class="col-4 demo-col">col-4</div>
        <div class="col-4 demo-col">col-4</div>
        <div class="col-4 demo-col">col-4</div>
      </div>
      <div class="row">
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
        <div class="col-3 demo-col">col-3</div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Mixed Column Sizes"
      description="Combine column classes to create complex layouts."
    >
      <div class="row">
        <div class="col-8 demo-col">col-8</div>
        <div class="col-4 demo-col">col-4</div>
      </div>
      <div class="row">
        <div class="col-4 demo-col">col-4</div>
        <div class="col-6 demo-col">col-6</div>
        <div class="col-2 demo-col">col-2</div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="Auto Columns"
      description="Let columns automatically size themselves based on content."
    >
      <div class="row">
        <div class="col demo-col">.col</div>
        <div class="col demo-col">.col</div>
        <div class="col demo-col">.col</div>
      </div>
      <div class="row">
        <div class="col demo-col">Flexible column</div>
        <div class="col-auto demo-col">.col-auto</div>
        <div class="col demo-col">Flexible column</div>
      </div>
    </ava-grid-demo-card>

    <ava-grid-demo-card
      title="No Gutters"
      description="Remove the space between columns with the .no-gutters class."
    >
      <div class="row no-gutters">
        <div class="col-6 demo-col">.col-6</div>
        <div class="col-6 demo-col">.col-6</div>
      </div>
    </ava-grid-demo-card>
  </div>
</div>
