.demo-card {
  // background-color: var(--color-background-primary, #ffffff);
  // border: 1px solid var(--color-border-subtle, #e5e7eb);
  border-radius: var(--global-radius-md, 8px);
  margin-bottom: var(--global-spacing-4, 2rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  // transition: box-shadow var(--global-motion-duration-standard, 0.15s)
  //   var(--global-motion-easing-standard, ease);

  // &:hover {
  //   box-shadow: var(--global-elevation-02, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  // }
}

.card-header {
  padding: var(--global-spacing-4, 1.5rem);
  border-bottom: 1px solid var(--color-border-subtle, #e5e7eb);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-title {
  font-size: var(--global-font-size-lg, 1.125rem);
  font-weight: var(--global-font-weight-semibold, 600);
  margin: 0 0 var(--global-spacing-2, 0.5rem) 0;
  color: var(--color-text-primary, #111827);
}

.card-description {
  font-size: var(--global-font-size-sm, 0.875rem);
  color: var(--color-text-secondary, #6b7280);
  margin: 0;
  line-height: var(--global-line-height-normal, 1.5);
}

.card-content {
  padding: var(--global-spacing-4, 1.5rem);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
