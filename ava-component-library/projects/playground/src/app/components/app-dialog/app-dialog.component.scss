.dialog-demo-page {
  min-height: 100vh;

  .demo-description {
    color: #6b7280;
    font-size: 16px;
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .dialog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
  }

  .dialog-example {
    .example-card {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .example-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.75rem;
      }

      .example-description {
        color: #6b7280;
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 1.5rem;
      }
    }
  }

  .demo-content {
    padding: 3rem 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
  }

  .col-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 0.5rem;
  }

  .box {
    padding: 2rem;
    color: black;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
  }

  h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  .dialog-demo-page {
    .dialog-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .example-card {
      padding: 1.5rem !important;
    }

    h1 {
      font-size: 2rem;
    }

    h2 {
      font-size: 1.5rem;
    }
  }
}