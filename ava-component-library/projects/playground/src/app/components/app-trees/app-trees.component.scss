/* Prevent horizontal scroll */
html,
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}
 
* {
  box-sizing: border-box;
}
 
/* Main layout */
.documentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;
 
  @media (max-width: 768px) {
    padding: 1rem;
  }
}
 
/* Header */
.doc-header {
  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }
 
  .description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 2rem;
  }
}
 
/* Sections */
.doc-sections {
  margin-top: 4rem;
}
 
.doc-section {
  margin-bottom: 4rem;
  border-bottom: 1px solid var(--border-color);
 
  &:last-child {
    border-bottom: none;
  }
 
  h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }
 
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 2rem 0 1rem 0;
  }
}
 
/* Section headers */
.section-header {
  cursor: pointer;
 
  .description-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
 
    p {
      flex: 1;
      margin: 0;
      color: var(--text-secondary);
      line-height: 1.6;
    }
 
    .code-toggle {
      background: var(--primary-color);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      font-weight: 500;
      white-space: nowrap;
      transition: background-color 0.2s ease;
 
      &:hover {
        background: var(--primary-hover);
      }
    }
  }
}
 
/* Demo navigation */
.demo-navigation {
  margin: 2rem 0;
 
  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
  }
 
  .demo-link {
    text-decoration: none;
    color: inherit;
    transition: transform 0.2s ease;
 
    &:hover {
      transform: translateY(-2px);
    }
  }
 
  .demo-card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    height: 100%;
    transition: all 0.2s ease;
 
    &:hover {
      border-color: var(--primary-color);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
 
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 0.5rem 0;
    }
 
    p {
      color: var(--text-secondary);
      margin: 0;
      line-height: 1.5;
    }
  }
}
 
/* Code examples */
.code-example {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 1rem;
 
  &.expanded {
    .example-preview {
      border-bottom: 1px solid var(--border-color);
    }
  }
}
 
.example-preview {
  background: var(--surface-color);
 
  .demo-note {
    background: var(--info-background);
    color: var(--info-text);
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    border-left: 4px solid var(--info-border);
  }
 
  .selection-info {
    margin-top: 1rem;
    padding: 0.75rem 1rem;
    background: var(--success-background);
    color: var(--success-text);
    border-radius: 4px;
    font-size: 0.875rem;
    border-left: 4px solid var(--success-border);
  }
}
 
.code-block {
  position: relative;
  background: var(--code-background);
  border-radius: 4px;
  overflow: hidden;
 
  .code-content {
    overflow-x: auto;
  }
 
  pre {
    margin: 0;
    padding: 1.5rem;
    overflow-x: auto;
    background: transparent;
 
    code {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 0.875rem;
      line-height: 1.5;
      color: var(--code-text);
      background: transparent;
      white-space: pre;
    }
  }
 
  .copy-button {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
 
    &:hover {
      background: var(--primary-hover);
    }
  }
}
 
/* API Reference */
.api-reference {
  .api-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    background: var(--surface-color);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border-color);
 
    th,
    td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }
 
    th {
      background: var(--header-background);
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
 
    td {
      color: var(--text-secondary);
      vertical-align: top;
 
      code {
        background: var(--code-inline-background);
        color: var(--code-inline-text);
        padding: 0.25rem 0.5rem;
        border-radius: 3px;
        font-size: 0.875rem;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
    }
 
    tr:last-child {
      th,
      td {
        border-bottom: none;
      }
    }
  }
}
 
/* Best Practices */
.best-practices {
  display: grid;
  gap: 1.5rem;
  margin-top: 1rem;
 
  .practice-item {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
 
    h4 {
      margin: 0 0 0.5rem 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
 
    p {
      margin: 0;
      color: var(--text-secondary);
      line-height: 1.6;
 
      code {
        background: var(--code-inline-background);
        color: var(--code-inline-text);
        padding: 0.25rem 0.5rem;
        border-radius: 3px;
        font-size: 0.875rem;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
    }
  }
}
 
/* Custom alert */
.custom-alert {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 300px;
 
  // Add backdrop
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
 
  .alert-content {
    padding: 20px;
    text-align: center;
 
    h4 {
      margin: 0 0 10px 0;
      color: var(--text-primary);
    }
 
    p {
      margin: 0 0 15px 0;
      color: var(--text-secondary);
    }
 
    button {
      background: var(--primary-color);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;
 
      &:hover {
        background: var(--primary-hover);
      }
    }
  }
}
 
/* Responsive adjustments */
@media (max-width: 768px) {
  .documentation {
    .doc-header {
      h1 {
        font-size: 2rem;
      }
 
      .description {
        font-size: 1rem;
      }
    }
 
    .section-header {
      .description-container {
        flex-direction: column;
        align-items: stretch;
 
        .code-toggle {
          align-self: flex-start;
        }
      }
    }
 
    .demo-navigation {
      .demo-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
    }
 
    .example-preview {
      padding: 1rem;
    }
 
    .api-reference {
      .api-table {
        font-size: 0.875rem;
 
        th,
        td {
          padding: 0.75rem 0.5rem;
        }
      }
    }
  }
}